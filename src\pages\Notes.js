import React, { useState } from 'react';
import { useNotes } from '../context/NotesContext';
import NoteModal from '../components/NoteModal';
import NoteViewer from '../components/NoteViewer';
import './Pages.css';

const Notes = () => {
  const { notes, loading, getCategories } = useNotes();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [viewingNoteId, setViewingNoteId] = useState(null);

  const categories = ['All', ...getCategories()];

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         note.preview.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (note.content && note.content.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || note.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCreateNote = () => {
    setEditingNote(null);
    setIsModalOpen(true);
  };

  const handleEditNote = (note) => {
    setEditingNote(note);
    setIsModalOpen(true);
  };

  const handleViewNote = (noteId) => {
    setViewingNoteId(noteId);
    setIsViewerOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingNote(null);
  };

  const handleCloseViewer = () => {
    setIsViewerOpen(false);
    setViewingNoteId(null);
  };

  const handleEditFromViewer = (note) => {
    setEditingNote(note);
    setIsModalOpen(true);
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="page-content">
          <div className="loading-container">
            <h2>Loading notes...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">All Notes</h1>
          <p className="page-description">
            Browse through all your notes and find what you're looking for
          </p>
          <button className="create-note-btn" onClick={handleCreateNote}>
            + Create New Note
          </button>
        </div>

        <div className="notes-controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="category-filter">
            {categories.map(category => (
              <button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div className="notes-grid">
          {filteredNotes.map(note => (
            <div key={note.id} className="note-card">
              <div className="note-header">
                <h3 className="note-title">{note.title}</h3>
                <span className="note-category">{note.category}</span>
              </div>
              <p className="note-preview">{note.preview}</p>
              <div className="note-footer">
                <span className="note-date">{note.date}</span>
                <div className="note-actions">
                  <button
                    className="note-button view-btn"
                    onClick={() => handleViewNote(note.id)}
                  >
                    Read More
                  </button>
                  <button
                    className="note-button edit-btn"
                    onClick={() => handleEditNote(note)}
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredNotes.length === 0 && (
          <div className="no-results">
            <h3>No notes found</h3>
            <p>Try adjusting your search terms or category filter.</p>
          </div>
        )}
      </div>

      {/* Note Creation/Editing Modal */}
      <NoteModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        editNote={editingNote}
      />

      {/* Note Viewer Modal */}
      <NoteViewer
        isOpen={isViewerOpen}
        onClose={handleCloseViewer}
        noteId={viewingNoteId}
        onEdit={handleEditFromViewer}
      />
    </div>
  );
};

export default Notes;
