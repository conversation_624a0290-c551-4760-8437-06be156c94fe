import React, { useState } from 'react';
import './Pages.css';

const Notes = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const notes = [
    {
      id: 1,
      title: "Java Fundamentals",
      category: "Programming",
      date: "2024-01-15",
      preview: "Basic concepts of Java programming including variables, methods, and classes..."
    },
    {
      id: 2,
      title: "React Hooks Guide",
      category: "Web Development",
      date: "2024-01-10",
      preview: "Complete guide to React hooks including useState, useEffect, and custom hooks..."
    },
    {
      id: 3,
      title: "Database Design Principles",
      category: "Database",
      date: "2024-01-05",
      preview: "Key principles for designing efficient and scalable database schemas..."
    },
    {
      id: 4,
      title: "Git Best Practices",
      category: "Tools",
      date: "2024-01-01",
      preview: "Essential Git commands and workflows for effective version control..."
    },
    {
      id: 5,
      title: "Algorithm Complexity",
      category: "Computer Science",
      date: "2023-12-28",
      preview: "Understanding Big O notation and analyzing algorithm performance..."
    },
    {
      id: 6,
      title: "CSS Grid Layout",
      category: "Web Development",
      date: "2023-12-25",
      preview: "Modern CSS Grid techniques for creating responsive layouts..."
    }
  ];

  const categories = ['All', 'Programming', 'Web Development', 'Database', 'Tools', 'Computer Science'];

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         note.preview.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || note.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">All Notes</h1>
          <p className="page-description">
            Browse through all your notes and find what you're looking for
          </p>
        </div>

        <div className="notes-controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="category-filter">
            {categories.map(category => (
              <button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div className="notes-grid">
          {filteredNotes.map(note => (
            <div key={note.id} className="note-card">
              <div className="note-header">
                <h3 className="note-title">{note.title}</h3>
                <span className="note-category">{note.category}</span>
              </div>
              <p className="note-preview">{note.preview}</p>
              <div className="note-footer">
                <span className="note-date">{note.date}</span>
                <button className="note-button">Read More</button>
              </div>
            </div>
          ))}
        </div>

        {filteredNotes.length === 0 && (
          <div className="no-results">
            <h3>No notes found</h3>
            <p>Try adjusting your search terms or category filter.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notes;
