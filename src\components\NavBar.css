/* NavBar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 0;
}

.navbar-scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  position: relative;
}

/* Brand/Logo */
.navbar-brand {
  text-decoration: none;
  color: #2c3e50;
  font-weight: 700;
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.navbar-brand:hover {
  color: #3498db;
}

.brand-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.navbar-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navbar-item {
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-item:hover {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.navbar-item.active {
  color: #3498db;
  background: rgba(52, 152, 219, 0.15);
}

.navbar-item.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: #3498db;
  border-radius: 1px;
}

/* Mobile Menu Button */
.navbar-burger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.navbar-burger:hover {
  background: rgba(52, 152, 219, 0.1);
}

.navbar-burger span {
  display: block;
  width: 20px;
  height: 2px;
  background: #2c3e50;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.navbar-burger.is-active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-burger.is-active span:nth-child(2) {
  opacity: 0;
}

.navbar-burger.is-active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.navbar-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar-mobile.is-active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.navbar-mobile-content {
  padding: 1rem 0;
}

.navbar-mobile-item {
  display: block;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  font-size: 1rem;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.navbar-mobile-item:hover {
  background: rgba(52, 152, 219, 0.1);
  border-left-color: #3498db;
}

.navbar-mobile-item.active {
  color: #3498db;
  background: rgba(52, 152, 219, 0.15);
  border-left-color: #3498db;
}

/* Mobile Menu Overlay */
.navbar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-container {
    padding: 1rem 1.5rem;
  }

  .navbar-menu {
    display: none;
  }

  .navbar-burger {
    display: flex;
  }

  .brand-text {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 1rem;
  }

  .brand-text {
    font-size: 1.2rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .navbar {
    background: rgba(30, 30, 30, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .navbar-scrolled {
    background: rgba(30, 30, 30, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  .navbar-brand,
  .navbar-item,
  .navbar-mobile-item {
    color: #e0e0e0;
  }

  .navbar-burger span {
    background: #e0e0e0;
  }

  .navbar-mobile {
    background: #2a2a2a;
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

/* Smooth page transitions */
body {
  padding-top: 80px; /* Account for fixed navbar */
}

/* Focus styles for accessibility */
.navbar-item:focus,
.navbar-mobile-item:focus,
.navbar-burger:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}
