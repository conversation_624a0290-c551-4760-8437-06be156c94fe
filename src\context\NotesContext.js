import React, { createContext, useContext, useState, useEffect } from 'react';

const NotesContext = createContext();

export const useNotes = () => {
  const context = useContext(NotesContext);
  if (!context) {
    throw new Error('useNotes must be used within a NotesProvider');
  }
  return context;
};

export const NotesProvider = ({ children }) => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);

  // Default categories
  const defaultCategories = ['Programming', 'Web Development', 'Database', 'Tools', 'Computer Science', 'Personal', 'Study'];

  // Load notes from localStorage on component mount
  useEffect(() => {
    const loadNotes = () => {
      try {
        const savedNotes = localStorage.getItem('myWebNotes');
        if (savedNotes) {
          const parsedNotes = JSON.parse(savedNotes);
          setNotes(parsedNotes);
        } else {
          // Set default notes if none exist
          const defaultNotes = [
            {
              id: 1,
              title: "Java Fundamentals",
              category: "Programming",
              content: "# Java Fundamentals\n\n## Variables and Data Types\n\nJava is a strongly typed language. Here are the basic data types:\n\n- **int**: Integer numbers (e.g., 42)\n- **double**: Decimal numbers (e.g., 3.14)\n- **String**: Text (e.g., \"Hello World\")\n- **boolean**: True or false values\n\n## Example Code\n\n```java\npublic class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}\n```\n\n## Key Concepts\n\n1. Object-Oriented Programming\n2. Platform Independence\n3. Memory Management\n4. Exception Handling",
              preview: "Basic concepts of Java programming including variables, methods, and classes...",
              date: new Date().toISOString().split('T')[0],
              tags: ["java", "programming", "basics"],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 2,
              title: "React Hooks Guide",
              category: "Web Development",
              content: "# React Hooks Complete Guide\n\n## useState Hook\n\nThe useState hook allows you to add state to functional components.\n\n```javascript\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>You clicked {count} times</p>\n      <button onClick={() => setCount(count + 1)}>\n        Click me\n      </button>\n    </div>\n  );\n}\n```\n\n## useEffect Hook\n\nThe useEffect hook lets you perform side effects in functional components.\n\n```javascript\nimport React, { useState, useEffect } from 'react';\n\nfunction Example() {\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    document.title = 'You clicked ' + count + ' times';\n  });\n\n  return (\n    <div>\n      <p>You clicked {count} times</p>\n      <button onClick={() => setCount(count + 1)}>\n        Click me\n      </button>\n    </div>\n  );\n}\n```",
              preview: "Complete guide to React hooks including useState, useEffect, and custom hooks...",
              date: new Date().toISOString().split('T')[0],
              tags: ["react", "hooks", "javascript"],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ];
          setNotes(defaultNotes);
          localStorage.setItem('myWebNotes', JSON.stringify(defaultNotes));
        }
      } catch (error) {
        console.error('Error loading notes:', error);
        setNotes([]);
      } finally {
        setLoading(false);
      }
    };

    loadNotes();
  }, []);

  // Save notes to localStorage whenever notes change
  useEffect(() => {
    if (!loading && notes.length > 0) {
      localStorage.setItem('myWebNotes', JSON.stringify(notes));
    }
  }, [notes, loading]);

  // Add a new note
  const addNote = (noteData) => {
    const newNote = {
      id: Date.now(), // Simple ID generation
      ...noteData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      date: new Date().toISOString().split('T')[0]
    };
    setNotes(prevNotes => [newNote, ...prevNotes]);
    return newNote;
  };

  // Update an existing note
  const updateNote = (id, updates) => {
    setNotes(prevNotes =>
      prevNotes.map(note =>
        note.id === id
          ? { ...note, ...updates, updatedAt: new Date().toISOString() }
          : note
      )
    );
  };

  // Delete a note
  const deleteNote = (id) => {
    setNotes(prevNotes => prevNotes.filter(note => note.id !== id));
  };

  // Get a note by ID
  const getNoteById = (id) => {
    return notes.find(note => note.id === parseInt(id));
  };

  // Get all unique categories
  const getCategories = () => {
    const noteCategories = [...new Set(notes.map(note => note.category))];
    return [...new Set([...defaultCategories, ...noteCategories])];
  };

  // Get all unique tags
  const getTags = () => {
    const allTags = notes.flatMap(note => note.tags || []);
    return [...new Set(allTags)];
  };

  const value = {
    notes,
    loading,
    addNote,
    updateNote,
    deleteNote,
    getNoteById,
    getCategories,
    getTags,
    defaultCategories
  };

  return (
    <NotesContext.Provider value={value}>
      {children}
    </NotesContext.Provider>
  );
};
