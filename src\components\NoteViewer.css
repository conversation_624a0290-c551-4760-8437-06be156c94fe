/* Note Viewer Styles */
.note-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
  backdrop-filter: blur(5px);
}

.note-viewer {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.note-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.note-viewer-title-section {
  flex: 1;
  margin-right: 1rem;
}

.note-viewer-title {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.note-viewer-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.note-viewer-category {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.note-viewer-date {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.note-viewer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.note-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.note-viewer-actions {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.action-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn:hover {
  background: rgba(52, 152, 219, 0.1);
}

.delete-btn:hover {
  background: rgba(231, 76, 60, 0.1);
}

.note-viewer-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.note-viewer-close:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.note-viewer-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.note-content {
  line-height: 1.8;
  color: #2c3e50;
}

/* Content Styling */
.content-h1 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid #667eea;
}

.content-h2 {
  color: #34495e;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
  padding-bottom: 0.3rem;
  border-bottom: 2px solid #95a5a6;
}

.content-h3 {
  color: #34495e;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 1.2rem 0 0.8rem 0;
}

.content-paragraph {
  margin: 1rem 0;
  text-align: justify;
}

.content-paragraph strong {
  font-weight: 700;
  color: #2c3e50;
}

.content-paragraph em {
  font-style: italic;
  color: #34495e;
}

.content-paragraph code {
  background: #f8f9fa;
  color: #e74c3c;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* Delete Confirmation Modal */
.delete-confirm-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}

.delete-confirm-modal {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  text-align: center;
}

.delete-confirm-modal h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.delete-confirm-modal p {
  color: #7f8c8d;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.delete-confirm-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: #ecf0f1;
  color: #7f8c8d;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .note-viewer-overlay {
    padding: 0.5rem;
  }

  .note-viewer {
    max-height: 95vh;
  }

  .note-viewer-header {
    padding: 1.5rem;
    flex-direction: column;
    align-items: stretch;
  }

  .note-viewer-title-section {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .note-viewer-actions {
    align-self: flex-end;
  }

  .note-viewer-title {
    font-size: 1.5rem;
  }

  .note-viewer-content {
    padding: 1.5rem;
  }

  .content-h1 {
    font-size: 1.5rem;
  }

  .content-h2 {
    font-size: 1.3rem;
  }

  .delete-confirm-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .note-viewer-header {
    padding: 1rem;
  }

  .note-viewer-content {
    padding: 1rem;
  }

  .note-viewer-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Custom scrollbar */
.note-viewer-content::-webkit-scrollbar {
  width: 6px;
}

.note-viewer-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.note-viewer-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.note-viewer-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
