import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Pages.css';

const Home = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [brightness, setBrightness] = useState(100);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  // Toggle brightness between max (100%) and min (30%)
  const toggleBrightness = () => {
    setBrightness(brightness === 100 ? 30 : 100);
  };

  useEffect(() => {
    // Trigger animations after component mounts
    setIsLoaded(true);

    // Add mouse tracking for interactive effects
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleJavaNotesClick = () => {
    navigate('/java');
  };

  const handleAllNotesClick = () => {
    navigate('/notes');
  };

  const handleContactClick = () => {
    navigate('/contact');
  };

  return (
    <div
      className="page-container"
      style={{
        filter: `brightness(${brightness}%)`,
        transition: 'filter 0.3s ease'
      }}
    >
      {/* Control Buttons */}
      <div className="control-buttons">
        <button
          className={`control-btn dark-mode-btn ${isDarkMode ? 'active' : ''}`}
          onClick={toggleDarkMode}
          title={isDarkMode ? 'Disable Dark Mode' : 'Enable Dark Mode'}
        >
          {isDarkMode ? '🌙' : '☀️'}
          <span>{isDarkMode ? 'Dark' : 'Light'}</span>
        </button>

        <button
          className={`control-btn brightness-btn ${brightness === 100 ? 'max' : 'min'}`}
          onClick={toggleBrightness}
          title={brightness === 100 ? 'Reduce Brightness' : 'Increase Brightness'}
        >
          {brightness === 100 ? '🔆' : '🔅'}
          <span>{brightness === 100 ? 'Max' : 'Min'}</span>
        </button>
      </div>

      {/* Animated Background Particles */}
      <div className="background-particles">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
        <div className="particle particle-5"></div>
        <div className="particle particle-6"></div>
      </div>

      {/* Floating Shapes */}
      <div className="floating-shapes">
        <div className="shape shape-circle"></div>
        <div className="shape shape-triangle"></div>
        <div className="shape shape-square"></div>
        <div className="shape shape-hexagon"></div>
      </div>

      {/* Animated Stars */}
      <div className="stars-container">
        <div className="star star-1">✦</div>
        <div className="star star-2">✧</div>
        <div className="star star-3">✦</div>
        <div className="star star-4">✧</div>
        <div className="star star-5">✦</div>
        <div className="star star-6">✨</div>
        <div className="star star-7">⭐</div>
        <div className="star star-8">✦</div>
      </div>

      {/* Neon Grid Lines */}
      <div className="neon-grid">
        <div className="grid-line horizontal line-1"></div>
        <div className="grid-line horizontal line-2"></div>
        <div className="grid-line vertical line-3"></div>
        <div className="grid-line vertical line-4"></div>
      </div>

      {/* Floating Orbs */}
      <div className="floating-orbs">
        <div className="orb orb-1"></div>
        <div className="orb orb-2"></div>
        <div className="orb orb-3"></div>
      </div>

      {/* Chemistry Laboratory Background */}
      <div className={`chemistry-lab ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>
        {/* Molecular Structures */}
        <div className="molecules">
          <div className="molecule molecule-1">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
          </div>

          <div className="molecule molecule-2">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="atom atom-4"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
            <div className="bond bond-4"></div>
          </div>

          <div className="molecule molecule-3">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
          </div>

          <div className="molecule molecule-4">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
          </div>

          <div className="molecule molecule-5">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
          </div>
        </div>

        {/* Chemical Reactions */}
        <div className="chemical-reactions">
          <div className="reaction reaction-1">
            <div className="reactant"></div>
            <div className="arrow">→</div>
            <div className="product"></div>
          </div>

          <div className="reaction reaction-2">
            <div className="reactant"></div>
            <div className="arrow">⇌</div>
            <div className="product"></div>
          </div>

          <div className="reaction reaction-3">
            <div className="reactant"></div>
            <div className="arrow">→</div>
            <div className="product"></div>
          </div>
        </div>

        {/* Atomic Particles */}
        <div className="atomic-particles">
          <div className="electron electron-1"></div>
          <div className="electron electron-2"></div>
          <div className="electron electron-3"></div>
          <div className="electron electron-4"></div>
          <div className="electron electron-5"></div>
          <div className="electron electron-6"></div>
          <div className="proton proton-1"></div>
          <div className="proton proton-2"></div>
          <div className="neutron neutron-1"></div>
          <div className="neutron neutron-2"></div>
        </div>

        {/* Chemical Formulas */}
        <div className="chemical-formulas">
          <div className="formula formula-1">H₂O</div>
          <div className="formula formula-2">CO₂</div>
          <div className="formula formula-3">C₆H₁₂O₆</div>
          <div className="formula formula-4">NaCl</div>
          <div className="formula formula-5">CH₄</div>
          <div className="formula formula-6">NH₃</div>
          <div className="formula formula-7">H₂SO₄</div>
          <div className="formula formula-8">CaCO₃</div>
        </div>

        {/* Laboratory Equipment */}
        <div className="lab-equipment">
          <div className="beaker beaker-1">
            <div className="liquid"></div>
            <div className="bubbles">
              <div className="bubble"></div>
              <div className="bubble"></div>
              <div className="bubble"></div>
            </div>
          </div>

          <div className="test-tube test-tube-1">
            <div className="liquid"></div>
          </div>

          <div className="flask flask-1">
            <div className="liquid"></div>
            <div className="vapor"></div>
          </div>

          <div className="beaker beaker-2">
            <div className="liquid"></div>
            <div className="bubbles">
              <div className="bubble"></div>
              <div className="bubble"></div>
              <div className="bubble"></div>
            </div>
          </div>

          <div className="test-tube test-tube-2">
            <div className="liquid"></div>
          </div>

          <div className="flask flask-2">
            <div className="liquid"></div>
            <div className="vapor"></div>
          </div>
        </div>

        {/* DNA Helix */}
        <div className="dna-helix">
          <div className="helix-strand strand-1"></div>
          <div className="helix-strand strand-2"></div>
          <div className="base-pair bp-1"></div>
          <div className="base-pair bp-2"></div>
          <div className="base-pair bp-3"></div>
          <div className="base-pair bp-4"></div>
        </div>
      </div>

      {/* Animated Waves */}
      <div className="waves-container">
        <div className="wave wave-1"></div>
        <div className="wave wave-2"></div>
        <div className="wave wave-3"></div>
      </div>

      <div className="page-content">
        <div
          className={`hero-section ${isLoaded ? 'loaded' : ''}`}
          style={{
            '--mouse-x': `${mousePosition.x}%`,
            '--mouse-y': `${mousePosition.y}%`,
          }}
        >
          <h1 className="hero-title">Welcome to MyWeb</h1>
          <p className="hero-subtitle">
            Your personal space for notes, learning, and knowledge sharing
          </p>
          <div className="hero-buttons">
            <button
              onClick={handleJavaNotesClick}
              className="btn btn-primary"
            >
              Explore Java Notes
            </button>
            <button
              onClick={handleAllNotesClick}
              className="btn btn-secondary"
            >
              View All Notes
            </button>
          </div>
        </div>

        <div className="features-section">
          <div className="features-grid">
            <div
              className="feature-card clickable-card"
              onClick={handleJavaNotesClick}
            >
              <div className="feature-icon">📚</div>
              <h3>Java Notes</h3>
              <p>Comprehensive Java programming notes and tutorials</p>
              <div className="card-action">Click to explore →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleAllNotesClick}
            >
              <div className="feature-icon">📝</div>
              <h3>General Notes</h3>
              <p>Various topics and learning materials organized for easy access</p>
              <div className="card-action">Click to view →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleContactClick}
            >
              <div className="feature-icon">📞</div>
              <h3>Contact</h3>
              <p>Get in touch for questions, feedback, or collaboration</p>
              <div className="card-action">Click to contact →</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
