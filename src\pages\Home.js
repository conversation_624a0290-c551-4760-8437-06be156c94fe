import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Pages.css';

const Home = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // Trigger animations after component mounts
    setIsLoaded(true);

    // Add mouse tracking for interactive effects
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleJavaNotesClick = () => {
    navigate('/java');
  };

  const handleAllNotesClick = () => {
    navigate('/notes');
  };

  const handleContactClick = () => {
    navigate('/contact');
  };

  return (
    <div className="page-container">
      {/* Animated Background Particles */}
      <div className="background-particles">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
        <div className="particle particle-5"></div>
        <div className="particle particle-6"></div>
      </div>

      {/* Floating Shapes */}
      <div className="floating-shapes">
        <div className="shape shape-circle"></div>
        <div className="shape shape-triangle"></div>
        <div className="shape shape-square"></div>
        <div className="shape shape-hexagon"></div>
      </div>

      {/* Animated Stars */}
      <div className="stars-container">
        <div className="star star-1">✦</div>
        <div className="star star-2">✧</div>
        <div className="star star-3">✦</div>
        <div className="star star-4">✧</div>
        <div className="star star-5">✦</div>
      </div>

      {/* Animated Waves */}
      <div className="waves-container">
        <div className="wave wave-1"></div>
        <div className="wave wave-2"></div>
        <div className="wave wave-3"></div>
      </div>

      <div className="page-content">
        <div
          className={`hero-section ${isLoaded ? 'loaded' : ''}`}
          style={{
            '--mouse-x': `${mousePosition.x}%`,
            '--mouse-y': `${mousePosition.y}%`,
          }}
        >
          <h1 className="hero-title">Welcome to MyWeb</h1>
          <p className="hero-subtitle">
            Your personal space for notes, learning, and knowledge sharing
          </p>
          <div className="hero-buttons">
            <button
              onClick={handleJavaNotesClick}
              className="btn btn-primary"
            >
              Explore Java Notes
            </button>
            <button
              onClick={handleAllNotesClick}
              className="btn btn-secondary"
            >
              View All Notes
            </button>
          </div>
        </div>

        <div className="features-section">
          <div className="features-grid">
            <div
              className="feature-card clickable-card"
              onClick={handleJavaNotesClick}
            >
              <div className="feature-icon">📚</div>
              <h3>Java Notes</h3>
              <p>Comprehensive Java programming notes and tutorials</p>
              <div className="card-action">Click to explore →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleAllNotesClick}
            >
              <div className="feature-icon">📝</div>
              <h3>General Notes</h3>
              <p>Various topics and learning materials organized for easy access</p>
              <div className="card-action">Click to view →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleContactClick}
            >
              <div className="feature-icon">📞</div>
              <h3>Contact</h3>
              <p>Get in touch for questions, feedback, or collaboration</p>
              <div className="card-action">Click to contact →</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
