import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Pages.css';

const Home = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [brightness, setBrightness] = useState(100);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  // Toggle brightness between max (100%) and min (30%)
  const toggleBrightness = () => {
    setBrightness(brightness === 100 ? 30 : 100);
  };

  useEffect(() => {
    // Trigger animations after component mounts
    setIsLoaded(true);

    // Add mouse tracking for interactive effects
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    // Live typing effect
    const typingTexts = [
      'const app = new React.App();',
      'npm install react-router-dom',
      'git commit -m "Added new features"',
      'console.log("Hello World!");',
      'import { useState } from "react";'
    ];

    let currentTextIndex = 0;
    let currentCharIndex = 0;
    let isDeleting = false;

    const typeWriter = () => {
      const typingElements = document.querySelectorAll('.typed-text');
      if (typingElements.length === 0) return;

      const currentText = typingTexts[currentTextIndex];
      const typingElement = typingElements[Math.floor(Math.random() * typingElements.length)];

      if (!isDeleting && currentCharIndex < currentText.length) {
        typingElement.textContent = currentText.substring(0, currentCharIndex + 1);
        currentCharIndex++;
        setTimeout(typeWriter, 100);
      } else if (isDeleting && currentCharIndex > 0) {
        typingElement.textContent = currentText.substring(0, currentCharIndex - 1);
        currentCharIndex--;
        setTimeout(typeWriter, 50);
      } else {
        isDeleting = !isDeleting;
        if (!isDeleting) {
          currentTextIndex = (currentTextIndex + 1) % typingTexts.length;
        }
        setTimeout(typeWriter, 1000);
      }
    };

    const typingInterval = setTimeout(typeWriter, 2000);

    window.addEventListener('mousemove', handleMouseMove);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(typingInterval);
    };
  }, []);

  const handleJavaNotesClick = () => {
    navigate('/java');
  };

  const handleAllNotesClick = () => {
    navigate('/notes');
  };

  const handleContactClick = () => {
    navigate('/contact');
  };

  return (
    <div
      className="page-container"
      style={{
        filter: `brightness(${brightness}%)`,
        transition: 'filter 0.3s ease'
      }}
    >
      {/* Control Buttons */}
      <div className="control-buttons">
        <button
          className={`control-btn dark-mode-btn ${isDarkMode ? 'active' : ''}`}
          onClick={toggleDarkMode}
          title={isDarkMode ? 'Disable Dark Mode' : 'Enable Dark Mode'}
        >
          {isDarkMode ? '🌙' : '☀️'}
          <span>{isDarkMode ? 'Dark' : 'Light'}</span>
        </button>

        <button
          className={`control-btn brightness-btn ${brightness === 100 ? 'max' : 'min'}`}
          onClick={toggleBrightness}
          title={brightness === 100 ? 'Reduce Brightness' : 'Increase Brightness'}
        >
          {brightness === 100 ? '🔆' : '🔅'}
          <span>{brightness === 100 ? 'Max' : 'Min'}</span>
        </button>
      </div>

      {/* Animated Background Particles */}
      <div className="background-particles">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
        <div className="particle particle-5"></div>
        <div className="particle particle-6"></div>
      </div>

      {/* Floating Shapes */}
      <div className="floating-shapes">
        <div className="shape shape-circle"></div>
        <div className="shape shape-triangle"></div>
        <div className="shape shape-square"></div>
        <div className="shape shape-hexagon"></div>
      </div>

      {/* Animated Stars */}
      <div className="stars-container">
        <div className="star star-1">✦</div>
        <div className="star star-2">✧</div>
        <div className="star star-3">✦</div>
        <div className="star star-4">✧</div>
        <div className="star star-5">✦</div>
        <div className="star star-6">✨</div>
        <div className="star star-7">⭐</div>
        <div className="star star-8">✦</div>
      </div>

      {/* Neon Grid Lines */}
      <div className="neon-grid">
        <div className="grid-line horizontal line-1"></div>
        <div className="grid-line horizontal line-2"></div>
        <div className="grid-line vertical line-3"></div>
        <div className="grid-line vertical line-4"></div>
      </div>

      {/* Floating Orbs */}
      <div className="floating-orbs">
        <div className="orb orb-1"></div>
        <div className="orb orb-2"></div>
        <div className="orb orb-3"></div>
      </div>

      {/* Coding Animation Background */}
      <div className={`coding-animation ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>
        {/* Animated Code Snippets */}
        <div className="code-snippets">
          <div className="code-block code-block-1">
            <div className="code-line">
              <span className="keyword">function</span>
              <span className="function-name"> calculateSum</span>
              <span className="bracket">(</span>
              <span className="parameter">a, b</span>
              <span className="bracket">) {'{'}</span>
            </div>
            <div className="code-line">
              <span className="indent">  </span>
              <span className="keyword">return</span>
              <span className="variable"> a </span>
              <span className="operator">+</span>
              <span className="variable"> b</span>
              <span className="semicolon">;</span>
            </div>
            <div className="code-line">
              <span className="bracket">{'}'}</span>
            </div>
          </div>

          <div className="code-block code-block-2">
            <div className="code-line">
              <span className="keyword">class</span>
              <span className="class-name"> ReactComponent</span>
              <span className="keyword"> extends</span>
              <span className="class-name"> Component</span>
              <span className="bracket"> {'{'}</span>
            </div>
            <div className="code-line">
              <span className="indent">  </span>
              <span className="method">render</span>
              <span className="bracket">() {'{'}</span>
            </div>
            <div className="code-line">
              <span className="indent">    </span>
              <span className="keyword">return</span>
              <span className="bracket"> (</span>
            </div>
            <div className="code-line">
              <span className="indent">      </span>
              <span className="tag">&lt;div</span>
              <span className="attribute"> className</span>
              <span className="operator">=</span>
              <span className="string">"app"</span>
              <span className="tag">&gt;</span>
            </div>
            <div className="code-line">
              <span className="indent">        </span>
              <span className="tag">&lt;h1&gt;</span>
              <span className="text">Hello World</span>
              <span className="tag">&lt;/h1&gt;</span>
            </div>
            <div className="code-line">
              <span className="indent">      </span>
              <span className="tag">&lt;/div&gt;</span>
            </div>
            <div className="code-line">
              <span className="indent">    </span>
              <span className="bracket">);</span>
            </div>
            <div className="code-line">
              <span className="indent">  </span>
              <span className="bracket">{'}'}</span>
            </div>
            <div className="code-line">
              <span className="bracket">{'}'}</span>
            </div>
          </div>

          <div className="code-block code-block-3">
            <div className="code-line">
              <span className="keyword">const</span>
              <span className="variable"> data </span>
              <span className="operator">=</span>
              <span className="keyword"> await</span>
              <span className="method"> fetch</span>
              <span className="bracket">(</span>
              <span className="string">'api/users'</span>
              <span className="bracket">);</span>
            </div>
            <div className="code-line">
              <span className="keyword">const</span>
              <span className="variable"> users </span>
              <span className="operator">=</span>
              <span className="keyword"> await</span>
              <span className="variable"> data</span>
              <span className="method">.json</span>
              <span className="bracket">();</span>
            </div>
            <div className="code-line">
              <span className="variable">console</span>
              <span className="method">.log</span>
              <span className="bracket">(</span>
              <span className="variable">users</span>
              <span className="bracket">);</span>
            </div>
          </div>

          <div className="code-block code-block-4">
            <div className="code-line">
              <span className="keyword">import</span>
              <span className="bracket"> {'{'}</span>
              <span className="variable"> useState, useEffect </span>
              <span className="bracket">{'}'}</span>
              <span className="keyword"> from</span>
              <span className="string"> 'react'</span>
              <span className="semicolon">;</span>
            </div>
            <div className="code-line">
              <span className="keyword">import</span>
              <span className="string"> './App.css'</span>
              <span className="semicolon">;</span>
            </div>
          </div>
        </div>

        {/* Programming Tags */}
        <div className="programming-tags">
          <div className="tag-item tag-1">JavaScript</div>
          <div className="tag-item tag-2">React</div>
          <div className="tag-item tag-3">Node.js</div>
          <div className="tag-item tag-4">Python</div>
          <div className="tag-item tag-5">HTML5</div>
          <div className="tag-item tag-6">CSS3</div>
          <div className="tag-item tag-7">MongoDB</div>
          <div className="tag-item tag-8">Express</div>
          <div className="tag-item tag-9">Git</div>
          <div className="tag-item tag-10">API</div>
          <div className="tag-item tag-11">JSON</div>
          <div className="tag-item tag-12">ES6+</div>
        </div>

        {/* Live Typing Effect */}
        <div className="live-typing">
          <div className="typing-line typing-1">
            <span className="cursor">|</span>
            <span className="typed-text"></span>
          </div>
          <div className="typing-line typing-2">
            <span className="cursor">|</span>
            <span className="typed-text"></span>
          </div>
        </div>
      </div>

      {/* Chemistry Laboratory Background */}
      <div className={`chemistry-lab ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>
        {/* Molecular Structures */}
        <div className="molecules">
          <div className="molecule molecule-1">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
          </div>

          <div className="molecule molecule-2">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="atom atom-4"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
            <div className="bond bond-4"></div>
          </div>

          <div className="molecule molecule-3">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
          </div>

          <div className="molecule molecule-4">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="atom atom-3"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
            <div className="bond bond-3"></div>
          </div>

          <div className="molecule molecule-5">
            <div className="atom atom-center"></div>
            <div className="atom atom-1"></div>
            <div className="atom atom-2"></div>
            <div className="bond bond-1"></div>
            <div className="bond bond-2"></div>
          </div>
        </div>

        {/* Chemical Reactions */}
        <div className="chemical-reactions">
          <div className="reaction reaction-1">
            <div className="reactant"></div>
            <div className="arrow">→</div>
            <div className="product"></div>
          </div>

          <div className="reaction reaction-2">
            <div className="reactant"></div>
            <div className="arrow">⇌</div>
            <div className="product"></div>
          </div>

          <div className="reaction reaction-3">
            <div className="reactant"></div>
            <div className="arrow">→</div>
            <div className="product"></div>
          </div>
        </div>

        {/* Atomic Particles */}
        <div className="atomic-particles">
          <div className="electron electron-1"></div>
          <div className="electron electron-2"></div>
          <div className="electron electron-3"></div>
          <div className="electron electron-4"></div>
          <div className="electron electron-5"></div>
          <div className="electron electron-6"></div>
          <div className="proton proton-1"></div>
          <div className="proton proton-2"></div>
          <div className="neutron neutron-1"></div>
          <div className="neutron neutron-2"></div>
        </div>

        {/* Chemical Formulas */}
        <div className="chemical-formulas">
          <div className="formula formula-1">H₂O</div>
          <div className="formula formula-2">CO₂</div>
          <div className="formula formula-3">C₆H₁₂O₆</div>
          <div className="formula formula-4">NaCl</div>
          <div className="formula formula-5">CH₄</div>
          <div className="formula formula-6">NH₃</div>
          <div className="formula formula-7">H₂SO₄</div>
          <div className="formula formula-8">CaCO₃</div>
        </div>

        {/* Laboratory Equipment */}
        <div className="lab-equipment">
          <div className="beaker beaker-1">
            <div className="liquid"></div>
            <div className="bubbles">
              <div className="bubble"></div>
              <div className="bubble"></div>
              <div className="bubble"></div>
            </div>
          </div>

          <div className="test-tube test-tube-1">
            <div className="liquid"></div>
          </div>

          <div className="flask flask-1">
            <div className="liquid"></div>
            <div className="vapor"></div>
          </div>

          <div className="beaker beaker-2">
            <div className="liquid"></div>
            <div className="bubbles">
              <div className="bubble"></div>
              <div className="bubble"></div>
              <div className="bubble"></div>
            </div>
          </div>

          <div className="test-tube test-tube-2">
            <div className="liquid"></div>
          </div>

          <div className="flask flask-2">
            <div className="liquid"></div>
            <div className="vapor"></div>
          </div>
        </div>

        {/* DNA Helix */}
        <div className="dna-helix">
          <div className="helix-strand strand-1"></div>
          <div className="helix-strand strand-2"></div>
          <div className="base-pair bp-1"></div>
          <div className="base-pair bp-2"></div>
          <div className="base-pair bp-3"></div>
          <div className="base-pair bp-4"></div>
        </div>
      </div>

      {/* Animated Waves */}
      <div className="waves-container">
        <div className="wave wave-1"></div>
        <div className="wave wave-2"></div>
        <div className="wave wave-3"></div>
      </div>

      <div className="page-content">
        <div
          className={`hero-section ${isLoaded ? 'loaded' : ''}`}
          style={{
            '--mouse-x': `${mousePosition.x}%`,
            '--mouse-y': `${mousePosition.y}%`,
          }}
        >
          <h1 className="hero-title">Welcome to MyWeb</h1>
          <p className="hero-subtitle">
            Your personal space for notes, learning, and knowledge sharing
          </p>
          <div className="hero-buttons">
            <button
              onClick={handleJavaNotesClick}
              className="btn btn-primary"
            >
              Explore Java Notes
            </button>
            <button
              onClick={handleAllNotesClick}
              className="btn btn-secondary"
            >
              View All Notes
            </button>
          </div>
        </div>

        <div className="features-section">
          <div className="features-grid">
            <div
              className="feature-card clickable-card"
              onClick={handleJavaNotesClick}
            >
              <div className="feature-icon">📚</div>
              <h3>Java Notes</h3>
              <p>Comprehensive Java programming notes and tutorials</p>
              <div className="card-action">Click to explore →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleAllNotesClick}
            >
              <div className="feature-icon">📝</div>
              <h3>General Notes</h3>
              <p>Various topics and learning materials organized for easy access</p>
              <div className="card-action">Click to view →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleContactClick}
            >
              <div className="feature-icon">📞</div>
              <h3>Contact</h3>
              <p>Get in touch for questions, feedback, or collaboration</p>
              <div className="card-action">Click to contact →</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
