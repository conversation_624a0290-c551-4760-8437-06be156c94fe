import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Pages.css';

const Home = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Trigger animations after component mounts
    setIsLoaded(true);
  }, []);

  const handleJavaNotesClick = () => {
    navigate('/java');
  };

  const handleAllNotesClick = () => {
    navigate('/notes');
  };

  const handleContactClick = () => {
    navigate('/contact');
  };

  return (
    <div className="page-container">











      <div className="page-content">
        <div className={`hero-section ${isLoaded ? 'loaded' : ''}`}>
          <h1 className="hero-title">JavaMastery</h1>
          <p className="hero-subtitle">
            Your personal space for notes, learning, and knowledge sharing
          </p>
          <div className="hero-buttons">
            <button
              onClick={handleJavaNotesClick}
              className="btn btn-primary"
            >
              Explore Java Notes
            </button>
            <button
              onClick={handleAllNotesClick}
              className="btn btn-secondary"
            >
              View All Notes
            </button>
          </div>
        </div>

        <div className="features-section">
          <div className="features-grid">
            <div
              className="feature-card clickable-card"
              onClick={handleJavaNotesClick}
            >
              <div className="feature-icon">📚</div>
              <h3>Java Notes</h3>
              <p>Comprehensive Java programming notes and tutorials</p>
              <div className="card-action">Click to explore →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleAllNotesClick}
            >
              <div className="feature-icon">📝</div>
              <h3>General Notes</h3>
              <p>Various topics and learning materials organized for easy access</p>
              <div className="card-action">Click to view →</div>
            </div>
            <div
              className="feature-card clickable-card"
              onClick={handleContactClick}
            >
              <div className="feature-icon">📞</div>
              <h3>Contact</h3>
              <p>Get in touch for questions, feedback, or collaboration</p>
              <div className="card-action">Click to contact →</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
