import React, { useState } from 'react';
import './Pages.css';

const JavaNotes = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState('beginner');

  const javaSyllabus = [
    {
      id: 1,
      title: "Java Fundamentals",
      icon: "☕",
      description: "Core Java concepts, syntax, and object-oriented programming basics",
      duration: "4-6 weeks",
      difficulty: "Beginner",
      topics: [
        "Introduction to Java and JVM",
        "Variables, Data Types, and Operators",
        "Control Structures (if-else, loops)",
        "Methods and Parameter Passing",
        "Arrays and Strings",
        "Object-Oriented Programming Concepts",
        "Classes and Objects",
        "Constructors and Method Overloading",
        "Inheritance and Polymorphism",
        "Encapsulation and Abstraction"
      ],
      studyMaterials: [
        {
          type: "Documentation",
          title: "Oracle Java Documentation",
          url: "https://docs.oracle.com/javase/tutorial/",
          description: "Official Java tutorials from Oracle"
        },
        {
          type: "Book",
          title: "Head First Java",
          author: "Kathy Sierra & Bert Bates",
          description: "Beginner-friendly Java programming book"
        },
        {
          type: "Online Course",
          title: "Java Programming Masterclass",
          platform: "Udemy",
          description: "Comprehensive Java course covering fundamentals to advanced topics"
        },
        {
          type: "Practice",
          title: "Codecademy Java Course",
          url: "https://www.codecademy.com/learn/learn-java",
          description: "Interactive Java programming exercises"
        }
      ],
      levelWiseContent: {
        beginner: {
          title: "🌱 Beginner Level - Java Fundamentals",
          description: "Start your Java journey with basic concepts and simple programming",
          concepts: [
            {
              name: "What is Java?",
              explanation: "Java is a popular programming language used to build applications for computers, phones, and websites. It's like learning a new language to talk to computers!",
              example: `// Your first Java program
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Welcome to Java!");
    }
}`
            },
            {
              name: "Variables and Data Types",
              explanation: "Variables are like containers that store different types of information. Think of them as labeled boxes where you keep your data.",
              example: `// Different types of data containers
public class Variables {
    public static void main(String[] args) {
        // Text container
        String name = "John";

        // Number containers
        int age = 25;
        double height = 5.9;

        // True/False container
        boolean isStudent = true;

        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Height: " + height);
        System.out.println("Is Student: " + isStudent);
    }
}`
            },
            {
              name: "Simple Classes and Objects",
              explanation: "A class is like a blueprint for creating objects. An object is like a real thing made from that blueprint.",
              example: `// Blueprint for a Car
public class Car {
    String color;
    String brand;

    // Method to start the car
    public void start() {
        System.out.println("The " + color + " " + brand + " is starting!");
    }

    public static void main(String[] args) {
        // Creating a car object from the blueprint
        Car myCar = new Car();
        myCar.color = "Red";
        myCar.brand = "Toyota";
        myCar.start();
    }
}`
            }
          ]
        },
        intermediate: {
          title: "🚀 Intermediate Level - Object-Oriented Programming",
          description: "Master the core principles of OOP and build more complex applications",
          concepts: [
            {
              name: "Encapsulation",
              explanation: "Encapsulation means keeping data private and providing controlled access through methods. It's like having a safe with a combination lock.",
              example: `public class BankAccount {
    private double balance; // Private data
    private String accountNumber;

    public BankAccount(String accountNumber, double initialBalance) {
        this.accountNumber = accountNumber;
        this.balance = initialBalance;
    }

    // Controlled access to balance
    public double getBalance() {
        return balance;
    }

    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            System.out.println("Deposited: $" + amount);
        }
    }

    public boolean withdraw(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            System.out.println("Withdrawn: $" + amount);
            return true;
        }
        return false;
    }
}`
            },
            {
              name: "Inheritance",
              explanation: "Inheritance allows one class to inherit properties and methods from another class. It's like children inheriting traits from their parents.",
              example: `// Parent class
class Animal {
    protected String name;
    protected int age;

    public void eat() {
        System.out.println(name + " is eating");
    }

    public void sleep() {
        System.out.println(name + " is sleeping");
    }
}

// Child class inheriting from Animal
class Dog extends Animal {
    private String breed;

    public Dog(String name, int age, String breed) {
        this.name = name;
        this.age = age;
        this.breed = breed;
    }

    // Dog-specific method
    public void bark() {
        System.out.println(name + " is barking: Woof!");
    }

    // Override parent method
    @Override
    public void eat() {
        System.out.println(name + " the " + breed + " is eating dog food");
    }
}`
            }
          ]
        },
        advanced: {
          title: "⚡ Advanced Level - Professional Java Development",
          description: "Learn advanced concepts used in enterprise applications and complex systems",
          concepts: [
            {
              name: "Collections and Generics",
              explanation: "Collections are advanced data structures that can hold multiple objects. Generics ensure type safety at compile time.",
              example: `import java.util.*;

public class CollectionsExample {
    public static void main(String[] args) {
        // Generic List - only accepts Strings
        List<String> names = new ArrayList<>();
        names.add("Alice");
        names.add("Bob");
        names.add("Charlie");

        // Generic Map - key-value pairs
        Map<String, Integer> ages = new HashMap<>();
        ages.put("Alice", 25);
        ages.put("Bob", 30);
        ages.put("Charlie", 28);

        // Enhanced for loop
        for (String name : names) {
            System.out.println(name + " is " + ages.get(name) + " years old");
        }

        // Stream API (Java 8+)
        names.stream()
             .filter(name -> ages.get(name) > 26)
             .forEach(name -> System.out.println(name + " is older than 26"));
    }
}`
            },
            {
              name: "Exception Handling",
              explanation: "Exception handling allows your program to gracefully handle errors and unexpected situations without crashing.",
              example: `import java.io.*;
import java.util.Scanner;

public class ExceptionHandling {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        try {
            System.out.print("Enter a number: ");
            int number = Integer.parseInt(scanner.nextLine());

            System.out.print("Enter divisor: ");
            int divisor = Integer.parseInt(scanner.nextLine());

            int result = divide(number, divisor);
            System.out.println("Result: " + result);

        } catch (NumberFormatException e) {
            System.err.println("Error: Please enter valid numbers");
        } catch (ArithmeticException e) {
            System.err.println("Error: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
        } finally {
            scanner.close();
            System.out.println("Program completed");
        }
    }

    public static int divide(int a, int b) throws ArithmeticException {
        if (b == 0) {
            throw new ArithmeticException("Cannot divide by zero");
        }
        return a / b;
    }
}`
            }
          ]
        }
      }
    },
    {
      id: 2,
      title: "Advanced Java Concepts",
      icon: "🔧",
      description: "Collections, multithreading, exception handling, and advanced OOP",
      duration: "6-8 weeks",
      difficulty: "Intermediate",
      topics: [
        "Exception Handling (try-catch-finally)",
        "Collections Framework (List, Set, Map)",
        "Generics and Type Safety",
        "Multithreading and Concurrency",
        "File I/O and Serialization",
        "Lambda Expressions and Functional Interfaces",
        "Stream API and Functional Programming",
        "Design Patterns (Singleton, Factory, Observer)",
        "Reflection and Annotations",
        "Memory Management and Garbage Collection"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Effective Java",
          author: "Joshua Bloch",
          description: "Best practices and advanced Java programming techniques"
        },
        {
          type: "Online Course",
          title: "Java Multithreading, Concurrency & Performance",
          platform: "Udemy",
          description: "Deep dive into Java concurrency and performance optimization"
        },
        {
          type: "Documentation",
          title: "Java Collections Framework Guide",
          url: "https://docs.oracle.com/javase/tutorial/collections/",
          description: "Comprehensive guide to Java Collections"
        },
        {
          type: "Practice",
          title: "LeetCode Java Problems",
          url: "https://leetcode.com/",
          description: "Algorithm and data structure problems in Java"
        }
      ],
      levelWiseContent: {
        beginner: {
          title: "🌱 Beginner Level - Introduction to Advanced Concepts",
          description: "Get familiar with more complex Java features step by step",
          concepts: [
            {
              name: "Exception Handling Basics",
              explanation: "Exceptions are problems that occur during program execution. Think of them as unexpected events that we need to handle gracefully, like a car breaking down - we need a plan!",
              example: `public class ExceptionBasics {
    public static void main(String[] args) {
        try {
            // This might cause an error
            int result = 10 / 0;
            System.out.println("Result: " + result);
        } catch (ArithmeticException e) {
            // Handle the error gracefully
            System.out.println("Oops! Cannot divide by zero!");
            System.out.println("Let's use a different number instead.");
            int safeResult = 10 / 2;
            System.out.println("Safe result: " + safeResult);
        }

        System.out.println("Program continues running!");
    }
}`
            },
            {
              name: "Simple Collections - ArrayList",
              explanation: "ArrayList is like a smart array that can grow and shrink. Imagine a magical box that can hold as many items as you want!",
              example: `import java.util.ArrayList;

public class SimpleArrayList {
    public static void main(String[] args) {
        // Create a list to store names
        ArrayList<String> friends = new ArrayList<>();

        // Add friends to the list
        friends.add("Alice");
        friends.add("Bob");
        friends.add("Charlie");

        System.out.println("My friends: " + friends);
        System.out.println("Number of friends: " + friends.size());

        // Check if someone is in the list
        if (friends.contains("Alice")) {
            System.out.println("Alice is my friend!");
        }

        // Remove a friend (sadly)
        friends.remove("Bob");
        System.out.println("Updated friends: " + friends);
    }
}`
            }
          ]
        },
        intermediate: {
          title: "🚀 Intermediate Level - Collections and Streams",
          description: "Master Java Collections Framework and modern programming techniques",
          concepts: [
            {
              name: "HashMap and Key-Value Storage",
              explanation: "HashMap stores data in key-value pairs, like a dictionary where you look up a word (key) to find its meaning (value).",
              example: `import java.util.HashMap;
import java.util.Map;

public class HashMapExample {
    public static void main(String[] args) {
        // Create a map to store student grades
        Map<String, Integer> grades = new HashMap<>();

        // Add student grades
        grades.put("Alice", 95);
        grades.put("Bob", 87);
        grades.put("Charlie", 92);

        // Get a specific grade
        System.out.println("Alice's grade: " + grades.get("Alice"));

        // Check if student exists
        if (grades.containsKey("Bob")) {
            System.out.println("Bob's grade: " + grades.get("Bob"));
        }

        // Print all grades
        for (Map.Entry<String, Integer> entry : grades.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }

        // Calculate average
        double average = grades.values().stream()
                              .mapToInt(Integer::intValue)
                              .average()
                              .orElse(0.0);
        System.out.println("Average grade: " + average);
    }
}`
            },
            {
              name: "Stream API Basics",
              explanation: "Streams allow you to process collections of data in a functional way. Think of it as an assembly line where each step transforms your data.",
              example: `import java.util.*;
import java.util.stream.Collectors;

public class StreamExample {
    public static void main(String[] args) {
        List<String> names = Arrays.asList("Alice", "Bob", "Charlie", "David", "Eve");

        // Filter names longer than 3 characters and convert to uppercase
        List<String> longNames = names.stream()
            .filter(name -> name.length() > 3)  // Keep only long names
            .map(String::toUpperCase)           // Convert to uppercase
            .sorted()                           // Sort alphabetically
            .collect(Collectors.toList());      // Collect results

        System.out.println("Long names (uppercase): " + longNames);

        // Count names starting with specific letter
        long countA = names.stream()
            .filter(name -> name.startsWith("A"))
            .count();

        System.out.println("Names starting with 'A': " + countA);

        // Find the longest name
        Optional<String> longest = names.stream()
            .max(Comparator.comparing(String::length));

        if (longest.isPresent()) {
            System.out.println("Longest name: " + longest.get());
        }
    }
}`
            }
          ]
        },
        advanced: {
          title: "⚡ Advanced Level - Concurrency and Performance",
          description: "Learn advanced concepts for building high-performance, concurrent applications",
          concepts: [
            {
              name: "Multithreading and Thread Safety",
              explanation: "Multithreading allows your program to do multiple things at once. It's like having multiple workers doing different tasks simultaneously, but they need to coordinate to avoid conflicts.",
              example: `import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadSafeCounter {
    private AtomicInteger count = new AtomicInteger(0);

    public void increment() {
        count.incrementAndGet();
    }

    public int getCount() {
        return count.get();
    }

    public static void main(String[] args) throws InterruptedException {
        ThreadSafeCounter counter = new ThreadSafeCounter();
        ExecutorService executor = Executors.newFixedThreadPool(10);

        // Submit 1000 increment tasks
        for (int i = 0; i < 1000; i++) {
            executor.submit(counter::increment);
        }

        // Shutdown and wait for completion
        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.SECONDS);

        System.out.println("Final count: " + counter.getCount());
        // Should print 1000 due to thread-safe AtomicInteger
    }
}`
            },
            {
              name: "CompletableFuture for Async Programming",
              explanation: "CompletableFuture allows you to write asynchronous, non-blocking code. It's like ordering food delivery - you don't wait by the door, you do other things until it arrives.",
              example: `import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class AsyncExample {
    public static void main(String[] args) throws ExecutionException, InterruptedException {

        // Simulate async operations
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
            sleep(1000); // Simulate 1 second work
            return "Data from Service 1";
        });

        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
            sleep(1500); // Simulate 1.5 seconds work
            return "Data from Service 2";
        });

        // Combine results when both complete
        CompletableFuture<String> combined = future1.thenCombine(future2,
            (result1, result2) -> result1 + " + " + result2);

        // Chain additional processing
        CompletableFuture<String> processed = combined.thenApply(result -> {
            return "Processed: " + result.toUpperCase();
        });

        // Handle completion
        processed.thenAccept(System.out::println);

        // Wait for completion
        processed.get();
    }

    private static void sleep(int millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}`
            }
          ]
        }
      }
    },
    {
      id: 3,
      title: "Java Web Development",
      icon: "🌐",
      description: "Servlets, JSP, Spring Framework, and web application development",
      duration: "8-10 weeks",
      difficulty: "Intermediate to Advanced",
      topics: [
        "Servlets and Servlet API",
        "JavaServer Pages (JSP)",
        "Model-View-Controller (MVC) Pattern",
        "Spring Framework Fundamentals",
        "Spring Boot and Auto-configuration",
        "Spring MVC and RESTful Web Services",
        "Spring Data JPA",
        "Spring Security",
        "Thymeleaf Template Engine",
        "Building and Deploying Web Applications"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Spring in Action",
          author: "Craig Walls",
          description: "Comprehensive guide to Spring Framework"
        },
        {
          type: "Online Course",
          title: "Spring Boot Microservices",
          platform: "Udemy",
          description: "Building microservices with Spring Boot"
        },
        {
          type: "Documentation",
          title: "Spring Framework Reference",
          url: "https://spring.io/projects/spring-framework",
          description: "Official Spring Framework documentation"
        },
        {
          type: "Tutorial",
          title: "Spring Boot Guides",
          url: "https://spring.io/guides",
          description: "Step-by-step Spring Boot tutorials"
        }
      ],
      codeExample: `// Spring Boot REST Controller Example
@RestController
@RequestMapping("/api/students")
public class StudentController {

    @Autowired
    private StudentService studentService;

    @GetMapping
    public ResponseEntity<List<Student>> getAllStudents() {
        List<Student> students = studentService.findAll();
        return ResponseEntity.ok(students);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Student> getStudentById(@PathVariable Long id) {
        Optional<Student> student = studentService.findById(id);
        return student.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<Student> createStudent(@RequestBody Student student) {
        Student savedStudent = studentService.save(student);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedStudent);
    }
}`
    },
    {
      id: 4,
      title: "Database Connectivity",
      icon: "💾",
      description: "JDBC, JPA, Hibernate, and database operations in Java",
      duration: "4-6 weeks",
      difficulty: "Intermediate",
      topics: [
        "JDBC Fundamentals",
        "Connection Management",
        "PreparedStatement and CallableStatement",
        "Result Set Processing",
        "Transaction Management",
        "Java Persistence API (JPA)",
        "Hibernate ORM Framework",
        "Entity Relationships and Mapping",
        "HQL and Criteria API",
        "Database Connection Pooling"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Java Persistence with Hibernate",
          author: "Christian Bauer & Gavin King",
          description: "Comprehensive guide to Hibernate and JPA"
        },
        {
          type: "Online Course",
          title: "Hibernate and JPA Fundamentals",
          platform: "Pluralsight",
          description: "Complete course on Hibernate and JPA"
        },
        {
          type: "Documentation",
          title: "Hibernate Documentation",
          url: "https://hibernate.org/orm/documentation/",
          description: "Official Hibernate ORM documentation"
        }
      ],
      codeExample: `// JPA Entity Example
@Entity
@Table(name = "students")
public class Student {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Column(name = "last_name", nullable = false)
    private String lastName;

    @Column(unique = true)
    private String email;

    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL)
    private List<Course> courses = new ArrayList<>();

    // Constructors, getters, and setters
    public Student() {}

    public Student(String firstName, String lastName, String email) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
    }
}`
    },
    {
      id: 5,
      title: "Testing in Java",
      icon: "🧪",
      description: "JUnit, TestNG, Mockito, and test-driven development",
      duration: "3-4 weeks",
      difficulty: "Intermediate",
      topics: [
        "Unit Testing Fundamentals",
        "JUnit 5 Framework",
        "Test Annotations and Assertions",
        "Parameterized Tests",
        "TestNG Framework",
        "Mocking with Mockito",
        "Integration Testing",
        "Test-Driven Development (TDD)",
        "Code Coverage Analysis",
        "Testing Best Practices"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Effective Unit Testing",
          author: "Lasse Koskela",
          description: "Best practices for writing effective unit tests"
        },
        {
          type: "Documentation",
          title: "JUnit 5 User Guide",
          url: "https://junit.org/junit5/docs/current/user-guide/",
          description: "Official JUnit 5 documentation"
        },
        {
          type: "Tutorial",
          title: "Mockito Tutorial",
          url: "https://www.baeldung.com/mockito-series",
          description: "Comprehensive Mockito tutorial series"
        }
      ],
      codeExample: `// JUnit 5 Test Example
@ExtendWith(MockitoExtension.class)
class StudentServiceTest {

    @Mock
    private StudentRepository studentRepository;

    @InjectMocks
    private StudentService studentService;

    @Test
    @DisplayName("Should find student by ID")
    void shouldFindStudentById() {
        // Given
        Long studentId = 1L;
        Student expectedStudent = new Student("John", "Doe", "<EMAIL>");
        when(studentRepository.findById(studentId)).thenReturn(Optional.of(expectedStudent));

        // When
        Optional<Student> result = studentService.findById(studentId);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getFirstName()).isEqualTo("John");
        verify(studentRepository).findById(studentId);
    }
}`
    }
  ];

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return '#27ae60';
      case 'Intermediate': return '#f39c12';
      case 'Intermediate to Advanced': return '#e67e22';
      case 'Advanced': return '#e74c3c';
      default: return '#3498db';
    }
  };

  const handleViewMore = (topic) => {
    setSelectedTopic(topic);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTopic(null);
  };



  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Java Programming Syllabus</h1>
          <p className="page-description">
            Complete Java learning path with study materials, code examples, and practice resources
          </p>
        </div>

        <div className="java-syllabus-grid">
          {javaSyllabus.map((topic) => (
            <div key={topic.id} className="syllabus-card">
              <div className="syllabus-header">
                <div className="syllabus-icon">{topic.icon}</div>
                <div className="syllabus-info">
                  <h3 className="syllabus-title">{topic.title}</h3>
                  <span
                    className="syllabus-difficulty"
                    style={{ backgroundColor: getDifficultyColor(topic.difficulty) }}
                  >
                    {topic.difficulty}
                  </span>
                </div>
              </div>
              <p className="syllabus-description">{topic.description}</p>
              <div className="syllabus-meta">
                <span className="syllabus-duration">📅 {topic.duration}</span>
                <span className="syllabus-topics">📚 {topic.topics.length} topics</span>
              </div>
              <button
                className="syllabus-button"
                onClick={() => handleViewMore(topic)}
              >
                View More
              </button>
            </div>
          ))}
        </div>

        <div className="learning-path">
          <h3>🎯 Recommended Learning Path</h3>
          <div className="path-steps">
            <div className="path-step">
              <span className="step-number">1</span>
              <span className="step-text">Start with Java Fundamentals</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">2</span>
              <span className="step-text">Master Advanced Concepts</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">3</span>
              <span className="step-text">Learn Database Connectivity</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">4</span>
              <span className="step-text">Practice Testing</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">5</span>
              <span className="step-text">Build Web Applications</span>
            </div>
          </div>
        </div>
      </div>

      {/* Full-Page Java Notes Modal */}
      {isModalOpen && selectedTopic && (
        <div className="fullpage-modal-overlay">
          <div className="fullpage-modal">
            {/* Modal Header */}
            <div className="fullpage-modal-header">
              <div className="modal-title-section">
                <span className="modal-icon">{selectedTopic.icon}</span>
                <h1 className="modal-title">{selectedTopic.title}</h1>
                <span
                  className="modal-difficulty"
                  style={{ backgroundColor: getDifficultyColor(selectedTopic.difficulty) }}
                >
                  {selectedTopic.difficulty}
                </span>
              </div>
              <button className="modal-close" onClick={handleCloseModal}>
                ✕
              </button>
            </div>

            {/* Level Selection Tabs */}
            <div className="level-tabs">
              <button
                className={`level-tab ${selectedLevel === 'beginner' ? 'active' : ''}`}
                onClick={() => setSelectedLevel('beginner')}
              >
                🌱 Beginner
              </button>
              <button
                className={`level-tab ${selectedLevel === 'intermediate' ? 'active' : ''}`}
                onClick={() => setSelectedLevel('intermediate')}
              >
                🚀 Intermediate
              </button>
              <button
                className={`level-tab ${selectedLevel === 'advanced' ? 'active' : ''}`}
                onClick={() => setSelectedLevel('advanced')}
              >
                ⚡ Advanced
              </button>
            </div>

            {/* Modal Content */}
            <div className="fullpage-modal-content">
              {selectedTopic.levelWiseContent && selectedTopic.levelWiseContent[selectedLevel] && (
                <div className="level-content">
                  <div className="level-header">
                    <h2>{selectedTopic.levelWiseContent[selectedLevel].title}</h2>
                    <p className="level-description">
                      {selectedTopic.levelWiseContent[selectedLevel].description}
                    </p>
                  </div>

                  <div className="concepts-grid">
                    {selectedTopic.levelWiseContent[selectedLevel].concepts.map((concept, index) => (
                      <div key={index} className="concept-card">
                        <div className="concept-header">
                          <h3 className="concept-name">{concept.name}</h3>
                        </div>
                        <div className="concept-explanation">
                          <p>{concept.explanation}</p>
                        </div>
                        <div className="concept-example">
                          <h4>💻 Code Example:</h4>
                          <pre className="code-block">
                            <code>{concept.example}</code>
                          </pre>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Study Materials Section */}
              <div className="modal-section">
                <h3>📖 Additional Study Materials</h3>
                <div className="study-materials">
                  {selectedTopic.studyMaterials.map((material, index) => (
                    <div key={index} className="material-card">
                      <div className="material-header">
                        <span className="material-type">{material.type}</span>
                        <h4 className="material-title">{material.title}</h4>
                        {material.author && (
                          <span className="material-author">by {material.author}</span>
                        )}
                        {material.platform && (
                          <span className="material-platform">on {material.platform}</span>
                        )}
                      </div>
                      <p className="material-description">{material.description}</p>
                      {material.url && (
                        <a
                          href={material.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="material-link"
                        >
                          Visit Resource →
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaNotes;
