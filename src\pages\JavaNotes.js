import React from 'react';
import './Pages.css';

const JavaNotes = () => {
  const javaTopics = [
    {
      title: "Java Basics",
      description: "Variables, data types, operators, and control structures",
      level: "Beginner"
    },
    {
      title: "Object-Oriented Programming",
      description: "Classes, objects, inheritance, polymorphism, and encapsulation",
      level: "Intermediate"
    },
    {
      title: "Collections Framework",
      description: "Lists, Sets, Maps, and their implementations",
      level: "Intermediate"
    },
    {
      title: "Exception Handling",
      description: "Try-catch blocks, custom exceptions, and best practices",
      level: "Intermediate"
    },
    {
      title: "Multithreading",
      description: "Threads, synchronization, and concurrent programming",
      level: "Advanced"
    },
    {
      title: "Stream API",
      description: "Functional programming with Java 8+ streams",
      level: "Advanced"
    }
  ];

  const getLevelColor = (level) => {
    switch (level) {
      case 'Beginner': return '#27ae60';
      case 'Intermediate': return '#f39c12';
      case 'Advanced': return '#e74c3c';
      default: return '#3498db';
    }
  };

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Java Programming Notes</h1>
          <p className="page-description">
            Comprehensive Java programming concepts, examples, and best practices
          </p>
        </div>

        <div className="topics-grid">
          {javaTopics.map((topic, index) => (
            <div key={index} className="topic-card">
              <div className="topic-header">
                <h3 className="topic-title">{topic.title}</h3>
                <span 
                  className="topic-level"
                  style={{ backgroundColor: getLevelColor(topic.level) }}
                >
                  {topic.level}
                </span>
              </div>
              <p className="topic-description">{topic.description}</p>
              <button className="topic-button">
                View Notes
              </button>
            </div>
          ))}
        </div>

        <div className="coming-soon">
          <h3>More Topics Coming Soon!</h3>
          <p>We're constantly adding new Java topics and examples. Stay tuned!</p>
        </div>
      </div>
    </div>
  );
};

export default JavaNotes;
