import React, { useState } from 'react';
import './Pages.css';

const JavaNotes = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const javaSyllabus = [
    {
      id: 1,
      title: "Java Fundamentals",
      icon: "☕",
      description: "Core Java concepts, syntax, and object-oriented programming basics",
      duration: "4-6 weeks",
      difficulty: "Beginner",
      topics: [
        "Introduction to Java and JVM",
        "Variables, Data Types, and Operators",
        "Control Structures (if-else, loops)",
        "Methods and Parameter Passing",
        "Arrays and Strings",
        "Object-Oriented Programming Concepts",
        "Classes and Objects",
        "Constructors and Method Overloading",
        "Inheritance and Polymorphism",
        "Encapsulation and Abstraction"
      ],
      studyMaterials: [
        {
          type: "Documentation",
          title: "Oracle Java Documentation",
          url: "https://docs.oracle.com/javase/tutorial/",
          description: "Official Java tutorials from Oracle"
        },
        {
          type: "Book",
          title: "Head First Java",
          author: "Kathy Sierra & Bert Bates",
          description: "Beginner-friendly Java programming book"
        },
        {
          type: "Online Course",
          title: "Java Programming Masterclass",
          platform: "Udemy",
          description: "Comprehensive Java course covering fundamentals to advanced topics"
        },
        {
          type: "Practice",
          title: "Codecademy Java Course",
          url: "https://www.codecademy.com/learn/learn-java",
          description: "Interactive Java programming exercises"
        }
      ],
      codeExample: `// Basic Java Class Example
public class Student {
    private String name;
    private int age;
    private String course;

    // Constructor
    public Student(String name, int age, String course) {
        this.name = name;
        this.age = age;
        this.course = course;
    }

    // Getter methods
    public String getName() {
        return name;
    }

    public int getAge() {
        return age;
    }

    // Method to display student info
    public void displayInfo() {
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Course: " + course);
    }

    public static void main(String[] args) {
        Student student = new Student("John Doe", 20, "Computer Science");
        student.displayInfo();
    }
}`
    },
    {
      id: 2,
      title: "Advanced Java Concepts",
      icon: "🔧",
      description: "Collections, multithreading, exception handling, and advanced OOP",
      duration: "6-8 weeks",
      difficulty: "Intermediate",
      topics: [
        "Exception Handling (try-catch-finally)",
        "Collections Framework (List, Set, Map)",
        "Generics and Type Safety",
        "Multithreading and Concurrency",
        "File I/O and Serialization",
        "Lambda Expressions and Functional Interfaces",
        "Stream API and Functional Programming",
        "Design Patterns (Singleton, Factory, Observer)",
        "Reflection and Annotations",
        "Memory Management and Garbage Collection"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Effective Java",
          author: "Joshua Bloch",
          description: "Best practices and advanced Java programming techniques"
        },
        {
          type: "Online Course",
          title: "Java Multithreading, Concurrency & Performance",
          platform: "Udemy",
          description: "Deep dive into Java concurrency and performance optimization"
        },
        {
          type: "Documentation",
          title: "Java Collections Framework Guide",
          url: "https://docs.oracle.com/javase/tutorial/collections/",
          description: "Comprehensive guide to Java Collections"
        },
        {
          type: "Practice",
          title: "LeetCode Java Problems",
          url: "https://leetcode.com/",
          description: "Algorithm and data structure problems in Java"
        }
      ],
      codeExample: `// Collections and Lambda Example
import java.util.*;
import java.util.stream.Collectors;

public class AdvancedJavaExample {
    public static void main(String[] args) {
        List<String> names = Arrays.asList("Alice", "Bob", "Charlie", "David");

        // Using Stream API with Lambda expressions
        List<String> filteredNames = names.stream()
            .filter(name -> name.length() > 4)
            .map(String::toUpperCase)
            .sorted()
            .collect(Collectors.toList());

        System.out.println("Filtered names: " + filteredNames);

        // Using HashMap
        Map<String, Integer> nameLength = new HashMap<>();
        names.forEach(name -> nameLength.put(name, name.length()));

        nameLength.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry ->
                System.out.println(entry.getKey() + ": " + entry.getValue()));
    }
}`
    },
    {
      id: 3,
      title: "Java Web Development",
      icon: "🌐",
      description: "Servlets, JSP, Spring Framework, and web application development",
      duration: "8-10 weeks",
      difficulty: "Intermediate to Advanced",
      topics: [
        "Servlets and Servlet API",
        "JavaServer Pages (JSP)",
        "Model-View-Controller (MVC) Pattern",
        "Spring Framework Fundamentals",
        "Spring Boot and Auto-configuration",
        "Spring MVC and RESTful Web Services",
        "Spring Data JPA",
        "Spring Security",
        "Thymeleaf Template Engine",
        "Building and Deploying Web Applications"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Spring in Action",
          author: "Craig Walls",
          description: "Comprehensive guide to Spring Framework"
        },
        {
          type: "Online Course",
          title: "Spring Boot Microservices",
          platform: "Udemy",
          description: "Building microservices with Spring Boot"
        },
        {
          type: "Documentation",
          title: "Spring Framework Reference",
          url: "https://spring.io/projects/spring-framework",
          description: "Official Spring Framework documentation"
        },
        {
          type: "Tutorial",
          title: "Spring Boot Guides",
          url: "https://spring.io/guides",
          description: "Step-by-step Spring Boot tutorials"
        }
      ],
      codeExample: `// Spring Boot REST Controller Example
@RestController
@RequestMapping("/api/students")
public class StudentController {

    @Autowired
    private StudentService studentService;

    @GetMapping
    public ResponseEntity<List<Student>> getAllStudents() {
        List<Student> students = studentService.findAll();
        return ResponseEntity.ok(students);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Student> getStudentById(@PathVariable Long id) {
        Optional<Student> student = studentService.findById(id);
        return student.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<Student> createStudent(@RequestBody Student student) {
        Student savedStudent = studentService.save(student);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedStudent);
    }
}`
    },
    {
      id: 4,
      title: "Database Connectivity",
      icon: "💾",
      description: "JDBC, JPA, Hibernate, and database operations in Java",
      duration: "4-6 weeks",
      difficulty: "Intermediate",
      topics: [
        "JDBC Fundamentals",
        "Connection Management",
        "PreparedStatement and CallableStatement",
        "Result Set Processing",
        "Transaction Management",
        "Java Persistence API (JPA)",
        "Hibernate ORM Framework",
        "Entity Relationships and Mapping",
        "HQL and Criteria API",
        "Database Connection Pooling"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Java Persistence with Hibernate",
          author: "Christian Bauer & Gavin King",
          description: "Comprehensive guide to Hibernate and JPA"
        },
        {
          type: "Online Course",
          title: "Hibernate and JPA Fundamentals",
          platform: "Pluralsight",
          description: "Complete course on Hibernate and JPA"
        },
        {
          type: "Documentation",
          title: "Hibernate Documentation",
          url: "https://hibernate.org/orm/documentation/",
          description: "Official Hibernate ORM documentation"
        }
      ],
      codeExample: `// JPA Entity Example
@Entity
@Table(name = "students")
public class Student {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Column(name = "last_name", nullable = false)
    private String lastName;

    @Column(unique = true)
    private String email;

    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL)
    private List<Course> courses = new ArrayList<>();

    // Constructors, getters, and setters
    public Student() {}

    public Student(String firstName, String lastName, String email) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
    }
}`
    },
    {
      id: 5,
      title: "Testing in Java",
      icon: "🧪",
      description: "JUnit, TestNG, Mockito, and test-driven development",
      duration: "3-4 weeks",
      difficulty: "Intermediate",
      topics: [
        "Unit Testing Fundamentals",
        "JUnit 5 Framework",
        "Test Annotations and Assertions",
        "Parameterized Tests",
        "TestNG Framework",
        "Mocking with Mockito",
        "Integration Testing",
        "Test-Driven Development (TDD)",
        "Code Coverage Analysis",
        "Testing Best Practices"
      ],
      studyMaterials: [
        {
          type: "Book",
          title: "Effective Unit Testing",
          author: "Lasse Koskela",
          description: "Best practices for writing effective unit tests"
        },
        {
          type: "Documentation",
          title: "JUnit 5 User Guide",
          url: "https://junit.org/junit5/docs/current/user-guide/",
          description: "Official JUnit 5 documentation"
        },
        {
          type: "Tutorial",
          title: "Mockito Tutorial",
          url: "https://www.baeldung.com/mockito-series",
          description: "Comprehensive Mockito tutorial series"
        }
      ],
      codeExample: `// JUnit 5 Test Example
@ExtendWith(MockitoExtension.class)
class StudentServiceTest {

    @Mock
    private StudentRepository studentRepository;

    @InjectMocks
    private StudentService studentService;

    @Test
    @DisplayName("Should find student by ID")
    void shouldFindStudentById() {
        // Given
        Long studentId = 1L;
        Student expectedStudent = new Student("John", "Doe", "<EMAIL>");
        when(studentRepository.findById(studentId)).thenReturn(Optional.of(expectedStudent));

        // When
        Optional<Student> result = studentService.findById(studentId);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getFirstName()).isEqualTo("John");
        verify(studentRepository).findById(studentId);
    }
}`
    }
  ];

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return '#27ae60';
      case 'Intermediate': return '#f39c12';
      case 'Intermediate to Advanced': return '#e67e22';
      case 'Advanced': return '#e74c3c';
      default: return '#3498db';
    }
  };

  const handleViewMore = (topic) => {
    setSelectedTopic(topic);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTopic(null);
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Java Programming Syllabus</h1>
          <p className="page-description">
            Complete Java learning path with study materials, code examples, and practice resources
          </p>
        </div>

        <div className="java-syllabus-grid">
          {javaSyllabus.map((topic) => (
            <div key={topic.id} className="syllabus-card">
              <div className="syllabus-header">
                <div className="syllabus-icon">{topic.icon}</div>
                <div className="syllabus-info">
                  <h3 className="syllabus-title">{topic.title}</h3>
                  <span
                    className="syllabus-difficulty"
                    style={{ backgroundColor: getDifficultyColor(topic.difficulty) }}
                  >
                    {topic.difficulty}
                  </span>
                </div>
              </div>
              <p className="syllabus-description">{topic.description}</p>
              <div className="syllabus-meta">
                <span className="syllabus-duration">📅 {topic.duration}</span>
                <span className="syllabus-topics">📚 {topic.topics.length} topics</span>
              </div>
              <button
                className="syllabus-button"
                onClick={() => handleViewMore(topic)}
              >
                View More
              </button>
            </div>
          ))}
        </div>

        <div className="learning-path">
          <h3>🎯 Recommended Learning Path</h3>
          <div className="path-steps">
            <div className="path-step">
              <span className="step-number">1</span>
              <span className="step-text">Start with Java Fundamentals</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">2</span>
              <span className="step-text">Master Advanced Concepts</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">3</span>
              <span className="step-text">Learn Database Connectivity</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">4</span>
              <span className="step-text">Practice Testing</span>
            </div>
            <div className="path-arrow">→</div>
            <div className="path-step">
              <span className="step-number">5</span>
              <span className="step-text">Build Web Applications</span>
            </div>
          </div>
        </div>
      </div>

      {/* Topic Detail Modal */}
      {isModalOpen && selectedTopic && (
        <div className="topic-modal-overlay" onClick={handleBackdropClick}>
          <div className="topic-modal">
            <div className="topic-modal-header">
              <div className="modal-title-section">
                <span className="modal-icon">{selectedTopic.icon}</span>
                <h2 className="modal-title">{selectedTopic.title}</h2>
              </div>
              <button className="modal-close" onClick={handleCloseModal}>×</button>
            </div>

            <div className="topic-modal-content">
              <div className="modal-section">
                <h3>📋 Course Overview</h3>
                <p>{selectedTopic.description}</p>
                <div className="course-meta">
                  <span className="meta-item">
                    <strong>Duration:</strong> {selectedTopic.duration}
                  </span>
                  <span className="meta-item">
                    <strong>Difficulty:</strong>
                    <span
                      className="difficulty-badge"
                      style={{ backgroundColor: getDifficultyColor(selectedTopic.difficulty) }}
                    >
                      {selectedTopic.difficulty}
                    </span>
                  </span>
                </div>
              </div>

              <div className="modal-section">
                <h3>📚 Topics Covered</h3>
                <ul className="topics-list">
                  {selectedTopic.topics.map((topic, index) => (
                    <li key={index} className="topic-item">{topic}</li>
                  ))}
                </ul>
              </div>

              <div className="modal-section">
                <h3>📖 Study Materials</h3>
                <div className="study-materials">
                  {selectedTopic.studyMaterials.map((material, index) => (
                    <div key={index} className="material-card">
                      <div className="material-header">
                        <span className="material-type">{material.type}</span>
                        <h4 className="material-title">{material.title}</h4>
                        {material.author && (
                          <span className="material-author">by {material.author}</span>
                        )}
                        {material.platform && (
                          <span className="material-platform">on {material.platform}</span>
                        )}
                      </div>
                      <p className="material-description">{material.description}</p>
                      {material.url && (
                        <a
                          href={material.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="material-link"
                        >
                          Visit Resource →
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="modal-section">
                <h3>💻 Code Example</h3>
                <pre className="code-example">
                  <code>{selectedTopic.codeExample}</code>
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaNotes;
