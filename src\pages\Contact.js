import React, { useState } from 'react';
import './Pages.css';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! I\'ll get back to you soon.');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Get In Touch</h1>
          <p className="page-description">
            Have questions, feedback, or want to collaborate? I'd love to hear from you!
          </p>
        </div>

        <div className="contact-container">
          <div className="contact-info">
            <h3>Connect With Me</h3>
            <p className="contact-intro">
              Let's connect! Click on any platform below to reach out or follow my work.
            </p>
            {/* <div className="contact-item">
              <div className="contact-icon">📧</div>
              <div>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div> */}
            <div className="contact-item">
              <a
                href="https://www.linkedin.com/in/pranav-jagadale-bb8678329/"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="LinkedIn"
              >
                <div className="contact-icon">💼</div>
              </a>
              <span className="contact-label">LinkedIn</span>
            </div>
            <div className="contact-item">
              <a
                href="https://github.com/pranav-jagadale"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="GitHub"
              >
                <div className="contact-icon">🐙</div>
              </a>
              <span className="contact-label">GitHub</span>
            </div>
            <div className="contact-item">
              <a
                href="mailto:<EMAIL>"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="Email"
              >
                <div className="contact-icon">📧</div>
              </a>
              <span className="contact-label">Email</span>
            </div>
            <div className="contact-item">
              <a
                href="http://localhost:3002"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="Portfolio"
              >
                <div className="contact-icon">🌐</div>
              </a>
              <span className="contact-label">Portfolio</span>
            </div>
            <div className="contact-item">
              <a
                href="https://codepen.io/pranav-jagadale"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="CodePen"
              >
                <div className="contact-icon">💻</div>
              </a>
              <span className="contact-label">CodePen</span>
            </div>
            <div className="contact-item">
              <a
                href="https://twitter.com/pranav_jagadale"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="Twitter"
              >
                <div className="contact-icon">�</div>
              </a>
              <span className="contact-label">Twitter</span>
            </div>
            <div className="contact-item">
              <a
                href="https://wa.me/919876543210"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-icon-link"
                title="WhatsApp"
              >
                <div className="contact-icon">�</div>
              </a>
              <span className="contact-label">WhatsApp</span>
            </div>
          </div>

          <form className="contact-form" onSubmit={handleSubmit}>
            <h3>Send a Message</h3>
            
            <div className="form-group">
              <label htmlFor="name">Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="subject">Subject *</label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="message">Message *</label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows="6"
                className="form-textarea"
              ></textarea>
            </div>

            <button type="submit" className="submit-button">
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Contact;
