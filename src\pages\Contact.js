import React, { useState } from 'react';
import './Pages.css';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! I\'ll get back to you soon.');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Get In Touch</h1>
          <p className="page-description">
            Have questions, feedback, or want to collaborate? I'd love to hear from you!
          </p>
        </div>

        <div className="contact-container">
          <div className="contact-info">
            <h3>Connect With Me</h3>
            <p className="contact-intro">
              Let's connect! Click on any platform below to reach out or follow my work.
            </p>
            <div className="contact-item">
              <div className="contact-icon">📧</div>
              <div>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div>
            <div className="contact-item">
              <div className="contact-icon">💼</div>
              <div>
                <h4>LinkedIn</h4>
                <a
                  href="https://www.linkedin.com/in/pranav-jagadale-bb8678329/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  linkedin.com/in/pranav-jagadale-bb8678329
                </a>
              </div>
            </div>
            <div className="contact-item">
              <div className="contact-icon">🐙</div>
              <div>
                <h4>GitHub</h4>
                <a
                  href="https://github.com/pranav-jagadale"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  github.com/pranav-jagadale
                </a>
              </div>
            </div>
            <div className="contact-item">
              <div className="contact-icon">📧</div>
              <div>
                <h4>Email Direct</h4>
                <a
                  href="mailto:<EMAIL>"
                  className="contact-link"
                >
                  Send Email
                </a>
              </div>
            </div>
            <div className="contact-item">
              <div className="contact-icon">🌐</div>
              <div>
                <h4>Portfolio</h4>
                <a
                  href="http://localhost:3002"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  View My Work
                </a>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">💻</div>
              <div>
                <h4>CodePen</h4>
                <a
                  href="https://codepen.io/pranav-jagadale"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  View Code Demos
                </a>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">📱</div>
              <div>
                <h4>Twitter</h4>
                <a
                  href="https://twitter.com/pranav_jagadale"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  Follow Updates
                </a>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">📞</div>
              <div>
                <h4>WhatsApp</h4>
                <a
                  href="https://wa.me/919876543210"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  Quick Message
                </a>
              </div>
            </div>
          </div>

          <form className="contact-form" onSubmit={handleSubmit}>
            <h3>Send a Message</h3>
            
            <div className="form-group">
              <label htmlFor="name">Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="subject">Subject *</label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="message">Message *</label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows="6"
                className="form-textarea"
              ></textarea>
            </div>

            <button type="submit" className="submit-button">
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Contact;
