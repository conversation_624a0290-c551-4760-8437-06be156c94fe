import React, { useState } from 'react';
import { useNotes } from '../context/NotesContext';
import './NoteViewer.css';

const NoteViewer = ({ isOpen, onClose, noteId, onEdit }) => {
  const { getNoteById, deleteNote } = useNotes();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  if (!isOpen || !noteId) return null;

  const note = getNoteById(noteId);
  
  if (!note) {
    return (
      <div className="note-viewer-overlay" onClick={onClose}>
        <div className="note-viewer">
          <div className="note-viewer-header">
            <h2>Note Not Found</h2>
            <button className="note-viewer-close" onClick={onClose}>×</button>
          </div>
          <div className="note-viewer-content">
            <p>The requested note could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleEdit = () => {
    onEdit(note);
    onClose();
  };

  const handleDelete = () => {
    deleteNote(note.id);
    setShowDeleteConfirm(false);
    onClose();
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderContent = (content) => {
    // Simple markdown-like rendering
    return content
      .split('\n')
      .map((line, index) => {
        // Headers
        if (line.startsWith('# ')) {
          return <h1 key={index} className="content-h1">{line.substring(2)}</h1>;
        }
        if (line.startsWith('## ')) {
          return <h2 key={index} className="content-h2">{line.substring(3)}</h2>;
        }
        if (line.startsWith('### ')) {
          return <h3 key={index} className="content-h3">{line.substring(4)}</h3>;
        }
        
        // Code blocks
        if (line.startsWith('```')) {
          return <div key={index} className="code-block-marker"></div>;
        }
        
        // Bold text
        line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Italic text
        line = line.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Inline code
        line = line.replace(/`(.*?)`/g, '<code>$1</code>');
        
        // Empty lines
        if (line.trim() === '') {
          return <br key={index} />;
        }
        
        // Regular paragraphs
        return (
          <p 
            key={index} 
            className="content-paragraph"
            dangerouslySetInnerHTML={{ __html: line }}
          />
        );
      });
  };

  return (
    <div className="note-viewer-overlay" onClick={handleBackdropClick}>
      <div className="note-viewer">
        <div className="note-viewer-header">
          <div className="note-viewer-title-section">
            <h1 className="note-viewer-title">{note.title}</h1>
            <div className="note-viewer-meta">
              <span className="note-viewer-category">{note.category}</span>
              <span className="note-viewer-date">
                Created: {formatDate(note.createdAt)}
              </span>
              {note.updatedAt !== note.createdAt && (
                <span className="note-viewer-date">
                  Updated: {formatDate(note.updatedAt)}
                </span>
              )}
            </div>
            {note.tags && note.tags.length > 0 && (
              <div className="note-viewer-tags">
                {note.tags.map((tag, index) => (
                  <span key={index} className="note-tag">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
          
          <div className="note-viewer-actions">
            <button 
              className="action-btn edit-btn" 
              onClick={handleEdit}
              title="Edit Note"
            >
              ✏️
            </button>
            <button 
              className="action-btn delete-btn" 
              onClick={() => setShowDeleteConfirm(true)}
              title="Delete Note"
            >
              🗑️
            </button>
            <button className="note-viewer-close" onClick={onClose}>
              ×
            </button>
          </div>
        </div>

        <div className="note-viewer-content">
          <div className="note-content">
            {renderContent(note.content)}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="delete-confirm-overlay">
            <div className="delete-confirm-modal">
              <h3>Delete Note</h3>
              <p>Are you sure you want to delete "{note.title}"? This action cannot be undone.</p>
              <div className="delete-confirm-actions">
                <button 
                  className="btn btn-secondary" 
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </button>
                <button 
                  className="btn btn-danger" 
                  onClick={handleDelete}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NoteViewer;
