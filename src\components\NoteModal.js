import React, { useState, useEffect } from 'react';
import { useNotes } from '../context/NotesContext';
import './NoteModal.css';

const NoteModal = ({ isOpen, onClose, editNote = null }) => {
  const { addNote, updateNote, getCategories } = useNotes();
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: '',
    tags: '',
    preview: ''
  });
  const [errors, setErrors] = useState({});

  // Reset form when modal opens/closes or when editNote changes
  useEffect(() => {
    if (isOpen) {
      if (editNote) {
        setFormData({
          title: editNote.title || '',
          content: editNote.content || '',
          category: editNote.category || '',
          tags: editNote.tags ? editNote.tags.join(', ') : '',
          preview: editNote.preview || ''
        });
      } else {
        setFormData({
          title: '',
          content: '',
          category: '',
          tags: '',
          preview: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, editNote]);

  // Auto-generate preview from content
  useEffect(() => {
    if (formData.content && !editNote) {
      const preview = formData.content
        .replace(/[#*`]/g, '') // Remove markdown symbols
        .substring(0, 150) + (formData.content.length > 150 ? '...' : '');
      setFormData(prev => ({ ...prev, preview }));
    }
  }, [formData.content, editNote]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }
    
    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const noteData = {
      title: formData.title.trim(),
      content: formData.content.trim(),
      category: formData.category.trim(),
      preview: formData.preview.trim() || formData.content.substring(0, 150) + '...',
      tags: formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)
    };

    if (editNote) {
      updateNote(editNote.id, noteData);
    } else {
      addNote(noteData);
    }

    onClose();
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const categories = getCategories();

  return (
    <div className="note-modal-overlay" onClick={handleBackdropClick}>
      <div className="note-modal">
        <div className="note-modal-header">
          <h2>{editNote ? 'Edit Note' : 'Create New Note'}</h2>
          <button className="note-modal-close" onClick={onClose}>
            ×
          </button>
        </div>

        <form className="note-modal-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`form-input ${errors.title ? 'error' : ''}`}
              placeholder="Enter note title..."
            />
            {errors.title && <span className="error-message">{errors.title}</span>}
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="category">Category *</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className={`form-select ${errors.category ? 'error' : ''}`}
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              {errors.category && <span className="error-message">{errors.category}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="tags">Tags</label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                className="form-input"
                placeholder="tag1, tag2, tag3..."
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="content">Content *</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleChange}
              className={`form-textarea ${errors.content ? 'error' : ''}`}
              placeholder="Write your note content here... (Markdown supported)"
              rows="12"
            />
            {errors.content && <span className="error-message">{errors.content}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="preview">Preview</label>
            <textarea
              id="preview"
              name="preview"
              value={formData.preview}
              onChange={handleChange}
              className="form-textarea"
              placeholder="Short preview of your note..."
              rows="3"
            />
          </div>

          <div className="note-modal-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              {editNote ? 'Update Note' : 'Create Note'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NoteModal;
