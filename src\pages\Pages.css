/* Common Page Styles */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
  position: relative;
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 1.2rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto 2rem auto;
  line-height: 1.6;
}

/* Create Note Button */
.create-note-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.create-note-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-container h2 {
  color: #7f8c8d;
  font-weight: 400;
}

/* Home Page Styles */



.page-content {
  position: relative;
  z-index: 1;
}

.hero-section {
  text-align: center;
  padding: 4rem 0;
  margin-bottom: 4rem;
  position: relative;
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #00ffff 0%, #ff0080 50%, #8a2be2 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slideInFromLeft 1s ease-out 0.3s both, titleGlow 3s ease infinite;
  position: relative;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: expandLine 1.5s ease-out 1s both;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #e0e0e0;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  animation: slideInFromRight 1s ease-out 0.6s both;
  opacity: 0;
  text-shadow: 0 0 20px rgba(224, 224, 224, 0.3);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.9s both;
  opacity: 0;
}

.hero-buttons .btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hero-buttons .btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.hero-buttons .btn:hover::before {
  width: 300px;
  height: 300px;
}

.hero-buttons .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hero-buttons .btn:active {
  transform: translateY(-1px);
  animation: pulse 0.3s ease;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.features-section {
  margin-top: 4rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease-out forwards;
}

.feature-card:nth-child(1) {
  animation-delay: 0.2s;
}

.feature-card:nth-child(2) {
  animation-delay: 0.4s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.6s;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: all 0.6s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.feature-card:active {
  transform: translateY(-5px) scale(0.98);
}

/* Clickable Card Styles */
.clickable-card {
  cursor: pointer;
}

.clickable-card:hover .card-action {
  opacity: 1;
  transform: translateY(0);
}

.card-action {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  opacity: 0;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clickable-card:active {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  animation: bounce 0.6s ease;
  transform: scale(1.2);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Java Notes Page Styles */
.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.topic-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.topic-card:hover {
  transform: translateY(-5px);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.topic-title {
  color: #2c3e50;
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
}

.topic-level {
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 1rem;
}

.topic-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.topic-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.coming-soon {
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.coming-soon h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.coming-soon p {
  color: #7f8c8d;
}

/* Notes Page Styles */
.notes-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.category-filter {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.note-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.note-card:hover {
  transform: translateY(-5px);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.note-title {
  color: #2c3e50;
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
}

.note-category {
  background: #ecf0f1;
  color: #7f8c8d;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  margin-left: 1rem;
}

.note-preview {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note-date {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
}

.note-button {
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.note-button.view-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.note-button.view-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.note-button.edit-btn {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid #3498db;
}

.note-button.edit-btn:hover {
  background: #3498db;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.no-results {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.no-results h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.no-results p {
  color: #7f8c8d;
}

/* Contact Page Styles */
.contact-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-top: 2rem;
}

.contact-info {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.contact-info h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.contact-label {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
  text-align: center;
  margin-top: 0.5rem;
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  text-align: center;
}

.contact-item h4 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.contact-item p {
  color: #7f8c8d;
  margin: 0;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-content {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .notes-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .contact-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .topic-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .topic-level {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .note-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .note-category {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .note-actions {
    flex-direction: column;
    gap: 0.3rem;
  }

  .note-button {
    width: 100%;
    text-align: center;
  }
}

/* Java Syllabus Styles */
.java-syllabus-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.syllabus-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.syllabus-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.syllabus-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.syllabus-icon {
  font-size: 3rem;
  margin-right: 1rem;
}

.syllabus-info {
  flex: 1;
}

.syllabus-title {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
}

.syllabus-difficulty {
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.syllabus-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.syllabus-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: #95a5a6;
}

.syllabus-duration,
.syllabus-topics {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.syllabus-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.syllabus-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Learning Path Styles */
.learning-path {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 3rem;
  text-align: center;
}

.learning-path h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.path-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.path-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
}

.step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.step-text {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 600;
}

.path-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* Topic Modal Styles */
.topic-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
  backdrop-filter: blur(5px);
}

.topic-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.topic-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-title-section {
  display: flex;
  align-items: center;
}

.modal-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
}

.modal-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.topic-modal-content {
  padding: 2rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.modal-section {
  margin-bottom: 2rem;
}

.modal-section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.course-meta {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
}

.difficulty-badge {
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.topics-list {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.5rem;
}

.topic-item {
  background: #f8f9fa;
  padding: 0.8rem 1rem;
  border-radius: 10px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.topic-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.study-materials {
  display: grid;
  gap: 1rem;
}

.material-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.material-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.material-header {
  margin-bottom: 1rem;
}

.material-type {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: inline-block;
}

.material-title {
  margin: 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.material-author,
.material-platform {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.material-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.material-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.material-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

.code-example {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1.5rem;
  border-radius: 10px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

.code-example code {
  background: none;
  color: inherit;
  padding: 0;
}

/* Responsive Styles for Java Syllabus */
@media (max-width: 768px) {
  .java-syllabus-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .syllabus-card {
    padding: 1.5rem;
  }

  .syllabus-header {
    flex-direction: column;
    text-align: center;
  }

  .syllabus-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .syllabus-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .path-steps {
    flex-direction: column;
    gap: 1.5rem;
  }

  .path-arrow {
    transform: rotate(90deg);
  }

  .topic-modal {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .topic-modal-header {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }

  .modal-title-section {
    margin-bottom: 1rem;
  }

  .topic-modal-content {
    padding: 1.5rem;
  }

  .course-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .topics-list {
    grid-template-columns: 1fr;
  }

  .material-card {
    padding: 1rem;
  }

  .code-example {
    font-size: 0.8rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .syllabus-card {
    padding: 1rem;
  }

  .learning-path {
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.4rem;
  }

  .modal-icon {
    font-size: 2rem;
  }

  .topic-modal-content {
    padding: 1rem;
  }
}

/* Custom scrollbar for topic modal */
.topic-modal-content::-webkit-scrollbar {
  width: 6px;
}

.topic-modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.topic-modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.topic-modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* Enhanced contact item styling */
.contact-item {
  margin-bottom: 1rem;
  padding: 0;
  background: transparent;
  border-radius: 15px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-icon-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  text-decoration: none;
  border: 2px solid transparent;
}

.contact-icon-link:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: rgba(102, 126, 234, 0.5);
}

.contact-icon-link .contact-icon {
  font-size: 2rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  line-height: 1;
  overflow: hidden;
  text-align: center;
}

.contact-icon-link:hover .contact-icon {
  transform: scale(1.2);
  filter: brightness(1.2);
}

.contact-intro {
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-style: italic;
  text-align: center;
}

/* Contact info grid layout */
.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contact-info h3 {
  grid-column: 1 / -1;
  text-align: center;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.contact-intro {
  grid-column: 1 / -1;
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Responsive adjustments for contact page */
@media (max-width: 768px) {
  .contact-info {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 1rem;
    max-width: 400px;
  }

  .contact-icon-link {
    width: 70px;
    height: 70px;
    border-radius: 15px;
  }

  .contact-icon-link .contact-icon {
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
  }

  .contact-item {
    margin-bottom: 0.5rem;
  }

  .contact-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .contact-info {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem;
  }

  .contact-icon-link {
    width: 60px;
    height: 60px;
  }

  .contact-icon-link .contact-icon {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
  }

  .contact-label {
    font-size: 0.7rem;
  }
}

/* Full-Page Modal Styles */
.fullpage-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.fullpage-modal {
  width: 95vw;
  height: 95vh;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.fullpage-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.fullpage-modal-header .modal-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.fullpage-modal-header .modal-icon {
  font-size: 2.5rem;
}

.fullpage-modal-header .modal-title {
  font-size: 2rem;
  margin: 0;
  font-weight: 700;
}

.fullpage-modal-header .modal-difficulty {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.fullpage-modal-header .modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fullpage-modal-header .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Level Selection Tabs */
.level-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.level-tab {
  flex: 1;
  padding: 1rem 2rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6c757d;
}

.level-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.level-tab.active {
  background: white;
  color: #667eea;
  border-bottom: 3px solid #667eea;
}

/* Modal Content */
.fullpage-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: #f8f9fa;
}

.level-content {
  max-width: 1200px;
  margin: 0 auto;
}

.level-header {
  text-align: center;
  margin-bottom: 3rem;
}

.level-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.level-description {
  font-size: 1.2rem;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Concepts Grid */
.concepts-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.concept-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.concept-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.concept-header h3 {
  color: #667eea;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
  padding-bottom: 0.5rem;
}

.concept-explanation {
  margin-bottom: 1.5rem;
}

.concept-explanation p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #495057;
}

.concept-example h4 {
  color: #28a745;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 10px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  border-left: 4px solid #667eea;
}

.code-block code {
  color: inherit;
  background: none;
  padding: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fullpage-modal {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .fullpage-modal-header {
    padding: 1rem;
  }

  .fullpage-modal-header .modal-title {
    font-size: 1.5rem;
  }

  .level-tabs {
    flex-direction: column;
  }

  .level-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .fullpage-modal-content {
    padding: 1rem;
  }

  .level-header h2 {
    font-size: 2rem;
  }

  .concept-card {
    padding: 1.5rem;
  }

  .code-block {
    font-size: 0.8rem;
    padding: 1rem;
  }
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from {
    width: 0;
  }
  to {
    width: 100px;
  }
}

@keyframes gradientShift {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  75% {
    transform: rotate(270deg) scale(1.1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.1);
  }
  60% {
    transform: translateY(-5px) scale(1.05);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Dark Background Animation Keyframes */
@keyframes darkGradientWave {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 100% 50%;
    filter: hue-rotate(90deg);
  }
  50% {
    background-position: 100% 100%;
    filter: hue-rotate(180deg);
  }
  75% {
    background-position: 0% 100%;
    filter: hue-rotate(270deg);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg);
  }
}

@keyframes darkFloatingBubbles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-40px) rotate(90deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: translateY(-80px) rotate(180deg) scale(0.9);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-40px) rotate(270deg) scale(1.2);
    opacity: 0.9;
  }
}

@keyframes glowingParticleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg) scale(0);
    opacity: 0;
    filter: brightness(1);
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) rotate(36deg) scale(1);
    filter: brightness(1.5);
  }
  50% {
    filter: brightness(2);
    transform: translateY(50vh) rotate(180deg) scale(1.2);
  }
  90% {
    opacity: 1;
    filter: brightness(1.5);
  }
  100% {
    transform: translateY(-100px) rotate(360deg) scale(0);
    opacity: 0;
    filter: brightness(1);
  }
}

@keyframes float1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(10px);
  }
  66% {
    transform: translateY(-10px) translateX(-5px);
  }
}

@keyframes float2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(45deg);
  }
  50% {
    transform: translateY(-30px) translateX(15px) rotate(225deg);
  }
}

@keyframes rotateFloat {
  0% {
    transform: rotate(0deg) translateY(0px);
  }
  50% {
    transform: rotate(180deg) translateY(-20px);
  }
  100% {
    transform: rotate(360deg) translateY(0px);
  }
}

@keyframes triangleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
}

@keyframes squareRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  75% {
    transform: rotate(270deg) scale(0.9);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes hexagonFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-25px) rotate(120deg);
  }
  66% {
    transform: translateY(-50px) rotate(240deg);
  }
}

@keyframes intenseTwinkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.3) rotate(90deg);
    filter: brightness(1.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
    filter: brightness(2);
  }
  75% {
    opacity: 0.8;
    transform: scale(1.3) rotate(270deg);
    filter: brightness(1.5);
  }
}

@keyframes colorShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes triangleGlow {
  0%, 100% {
    filter: drop-shadow(0 0 30px #00ff41) drop-shadow(0 0 60px #00ff41);
  }
  50% {
    filter: drop-shadow(0 0 50px #00ff41) drop-shadow(0 0 100px #00ff41) drop-shadow(0 0 150px #00ff41);
  }
}

@keyframes squareColorShift {
  0% { background-position: 0% 50%; }
  33% { background-position: 100% 50%; }
  66% { background-position: 50% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes hexagonGlow {
  0%, 100% {
    box-shadow: 0 0 50px #ff4500, 0 0 100px rgba(255, 69, 0, 0.6);
  }
  50% {
    box-shadow: 0 0 80px #ff4500, 0 0 160px rgba(255, 69, 0, 0.8), 0 0 240px rgba(255, 69, 0, 0.4);
  }
}

@keyframes neonPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.5);
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-30px) translateX(20px) scale(1.1);
    opacity: 0.8;
  }
  66% {
    transform: translateY(-60px) translateX(-10px) scale(0.9);
    opacity: 1;
  }
}

@keyframes titleGlow {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.3);
  }
}

/* Control Buttons */
.control-buttons {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #00ffff;
  border-radius: 25px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.control-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  border-color: #ffffff;
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.5);
  transform: translateY(-2px);
}

.dark-mode-btn.active {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  box-shadow: 0 0 25px #00ffff;
}

.brightness-btn.max {
  border-color: #ffff00;
  box-shadow: 0 4px 15px rgba(255, 255, 0, 0.3);
}

.brightness-btn.max:hover {
  border-color: #ffff00;
  box-shadow: 0 6px 20px rgba(255, 255, 0, 0.5);
}

.brightness-btn.min {
  border-color: #ff8800;
  box-shadow: 0 4px 15px rgba(255, 136, 0, 0.3);
}

.brightness-btn.min:hover {
  border-color: #ff8800;
  box-shadow: 0 6px 20px rgba(255, 136, 0, 0.5);
}

/* Coding Animation Background */
.coding-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: -2;
  overflow: hidden;
  opacity: 0.3;
}

.coding-animation.dark-mode {
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(20, 20, 40, 0.8) 50%,
    rgba(0, 0, 0, 0.9) 100%);
}

.coding-animation.light-mode {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(230, 240, 250, 0.8) 50%,
    rgba(248, 250, 252, 0.9) 100%);
}

/* Code Snippets */
.code-snippets {
  position: absolute;
  width: 100%;
  height: 100%;
}

.code-block {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  backdrop-filter: blur(5px);
  animation: codeFloat 20s ease-in-out infinite;
}

.coding-animation.light-mode .code-block {
  background: rgba(255, 255, 255, 0.9);
  border-color: #ddd;
  color: #333;
}

.code-block-1 {
  top: 10%;
  left: 5%;
  animation-delay: 0s;
}

.code-block-2 {
  top: 60%;
  right: 10%;
  animation-delay: 5s;
}

.code-block-3 {
  bottom: 20%;
  left: 15%;
  animation-delay: 10s;
}

.code-block-4 {
  top: 30%;
  left: 70%;
  animation-delay: 15s;
}

.code-line {
  margin: 2px 0;
  white-space: nowrap;
  animation: typingEffect 3s ease-in-out infinite;
}

/* Code Syntax Highlighting */
.keyword {
  color: #ff6b6b;
  font-weight: bold;
}

.function-name {
  color: #4ecdc4;
}

.class-name {
  color: #45b7d1;
}

.method {
  color: #96ceb4;
}

.variable {
  color: #feca57;
}

.parameter {
  color: #ff9ff3;
}

.string {
  color: #a8e6cf;
}

.bracket {
  color: #dda0dd;
}

.operator {
  color: #ff8a80;
}

.semicolon {
  color: #b0bec5;
}

.tag {
  color: #81c784;
}

.attribute {
  color: #ffb74d;
}

.text {
  color: #e1bee7;
}

.indent {
  color: transparent;
}

/* Light mode syntax colors */
.coding-animation.light-mode .keyword {
  color: #d32f2f;
}

.coding-animation.light-mode .function-name {
  color: #00796b;
}

.coding-animation.light-mode .class-name {
  color: #1976d2;
}

.coding-animation.light-mode .method {
  color: #388e3c;
}

.coding-animation.light-mode .variable {
  color: #f57c00;
}

.coding-animation.light-mode .parameter {
  color: #7b1fa2;
}

.coding-animation.light-mode .string {
  color: #2e7d32;
}

.coding-animation.light-mode .bracket {
  color: #5e35b1;
}

.coding-animation.light-mode .operator {
  color: #c62828;
}

.coding-animation.light-mode .semicolon {
  color: #424242;
}

.coding-animation.light-mode .tag {
  color: #2e7d32;
}

.coding-animation.light-mode .attribute {
  color: #ef6c00;
}

.coding-animation.light-mode .text {
  color: #6a1b9a;
}

/* Programming Tags */
.programming-tags {
  position: absolute;
  width: 100%;
  height: 100%;
}

.tag-item {
  position: absolute;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  animation: tagFloat 15s ease-in-out infinite;
  backdrop-filter: blur(5px);
}

.coding-animation.light-mode .tag-item {
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
  color: #333;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.tag-1 {
  top: 15%;
  left: 20%;
  animation-delay: 0s;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
}

.tag-2 {
  top: 25%;
  right: 25%;
  animation-delay: 2s;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
}

.tag-3 {
  bottom: 30%;
  left: 10%;
  animation-delay: 4s;
  background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
}

.tag-4 {
  top: 50%;
  left: 50%;
  animation-delay: 6s;
  background: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
}

.tag-5 {
  bottom: 15%;
  right: 15%;
  animation-delay: 8s;
  background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%);
}

.tag-6 {
  top: 70%;
  left: 30%;
  animation-delay: 10s;
  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
}

.tag-7 {
  top: 40%;
  right: 40%;
  animation-delay: 12s;
  background: linear-gradient(45deg, #a18cd1 0%, #fbc2eb 100%);
}

.tag-8 {
  bottom: 50%;
  left: 60%;
  animation-delay: 14s;
  background: linear-gradient(45deg, #fad0c4 0%, #ffd1ff 100%);
}

.tag-9 {
  top: 80%;
  right: 30%;
  animation-delay: 16s;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.tag-10 {
  top: 35%;
  left: 80%;
  animation-delay: 18s;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
}

.tag-11 {
  bottom: 40%;
  right: 50%;
  animation-delay: 20s;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
}

.tag-12 {
  top: 60%;
  left: 15%;
  animation-delay: 22s;
  background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
}

/* Live Typing Effect */
.live-typing {
  position: absolute;
  width: 100%;
  height: 100%;
}

.typing-line {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #00ff00;
  border-radius: 5px;
  padding: 10px 15px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #00ff00;
  backdrop-filter: blur(5px);
  min-width: 200px;
}

.coding-animation.light-mode .typing-line {
  background: rgba(255, 255, 255, 0.9);
  border-color: #2e7d32;
  color: #2e7d32;
}

.typing-1 {
  top: 20%;
  right: 5%;
  animation: typeWriter1 8s ease-in-out infinite;
}

.typing-2 {
  bottom: 10%;
  left: 40%;
  animation: typeWriter2 10s ease-in-out infinite;
  animation-delay: 4s;
}

.cursor {
  animation: blink 1s infinite;
  font-weight: bold;
}

.typed-text {
  margin-left: 5px;
}

/* Chemistry Laboratory Styles */
.chemistry-lab {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  transition: all 0.5s ease;
}

.chemistry-lab.dark-mode {
  background: radial-gradient(ellipse at center,
    rgba(0, 20, 40, 0.9) 0%,
    rgba(0, 0, 0, 0.95) 100%);
}

.chemistry-lab.light-mode {
  background: radial-gradient(ellipse at center,
    rgba(240, 248, 255, 0.9) 0%,
    rgba(200, 220, 240, 0.95) 100%);
}

/* Molecular Structures */
.molecules {
  position: absolute;
  width: 100%;
  height: 100%;
}

.molecule {
  position: absolute;
  animation: moleculeRotate 20s linear infinite;
}

.molecule-1 {
  top: 10%;
  left: 5%;
  width: 140px;
  height: 140px;
  animation-delay: 0s;
}

.molecule-2 {
  top: 50%;
  right: 8%;
  width: 160px;
  height: 160px;
  animation-delay: 5s;
}

.molecule-3 {
  bottom: 15%;
  left: 60%;
  width: 120px;
  height: 120px;
  animation-delay: 10s;
}

.molecule-4 {
  top: 75%;
  left: 40%;
  width: 130px;
  height: 130px;
  animation-delay: 15s;
}

.molecule-5 {
  top: 35%;
  left: 80%;
  width: 110px;
  height: 110px;
  animation-delay: 20s;
}

.atom {
  position: absolute;
  border-radius: 50%;
  animation: atomPulse 3s ease-in-out infinite;
}

.atom-center {
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #00ffff 0%, #0080ff 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 30px #00ffff;
  animation-delay: 0s;
}

/* Light mode chemistry variations */
.chemistry-lab.light-mode .atom-center {
  background: radial-gradient(circle, #0066cc 0%, #003d7a 100%);
  box-shadow: 0 0 30px #0066cc;
}

.chemistry-lab.light-mode .atom-1 {
  background: radial-gradient(circle, #cc0066 0%, #800040 100%);
  box-shadow: 0 0 25px #cc0066;
}

.chemistry-lab.light-mode .atom-2 {
  background: radial-gradient(circle, #00cc66 0%, #008040 100%);
  box-shadow: 0 0 25px #00cc66;
}

.chemistry-lab.light-mode .atom-3 {
  background: radial-gradient(circle, #cc6600 0%, #804000 100%);
  box-shadow: 0 0 25px #cc6600;
}

.chemistry-lab.light-mode .bond {
  background: linear-gradient(90deg, transparent, #333333, transparent);
  box-shadow: 0 0 10px #333333;
}

.chemistry-lab.light-mode .formula {
  color: #333333;
  text-shadow: 0 0 10px currentColor;
}

.chemistry-lab.light-mode .electron {
  background: radial-gradient(circle, #0066cc 0%, #003d7a 100%);
  box-shadow: 0 0 20px #0066cc;
}

.chemistry-lab.light-mode .proton {
  background: radial-gradient(circle, #cc0066 0%, #800040 100%);
  box-shadow: 0 0 25px #cc0066;
}

.chemistry-lab.light-mode .neutron {
  background: radial-gradient(circle, #6600cc 0%, #400080 100%);
  box-shadow: 0 0 22px #6600cc;
}

.chemistry-lab.light-mode .beaker {
  border-color: #0066cc;
  box-shadow: 0 0 30px #0066cc;
}

.chemistry-lab.light-mode .test-tube {
  border-color: #00cc66;
  box-shadow: 0 0 25px #00cc66;
}

.chemistry-lab.light-mode .flask {
  border-color: #cc6600;
  box-shadow: 0 0 35px #cc6600;
}

.chemistry-lab.light-mode .beaker .liquid {
  background: linear-gradient(0deg, #cc0066 0%, #ff4080 100%);
}

.chemistry-lab.light-mode .test-tube .liquid {
  background: linear-gradient(0deg, #6600cc 0%, #9040ff 100%);
}

.chemistry-lab.light-mode .flask .liquid {
  background: linear-gradient(0deg, #0066cc 0%, #40ccff 100%);
}

.chemistry-lab.light-mode .helix-strand {
  opacity: 0.8;
}

.chemistry-lab.light-mode .strand-1 {
  background: linear-gradient(0deg, #0066cc 0%, #cc0066 50%, #0066cc 100%);
  box-shadow: 0 0 20px #0066cc;
}

.chemistry-lab.light-mode .strand-2 {
  background: linear-gradient(0deg, #cc0066 0%, #0066cc 50%, #cc0066 100%);
  box-shadow: 0 0 20px #cc0066;
}

.chemistry-lab.light-mode .base-pair {
  background: linear-gradient(90deg, #00cc66 0%, #333333 50%, #cc6600 100%);
  box-shadow: 0 0 15px #333333;
}

.atom-1 {
  width: 12px;
  height: 12px;
  background: radial-gradient(circle, #ff0080 0%, #ff4080 100%);
  top: 20%;
  left: 20%;
  box-shadow: 0 0 20px #ff0080;
  animation-delay: 0.5s;
}

.atom-2 {
  width: 14px;
  height: 14px;
  background: radial-gradient(circle, #00ff41 0%, #40ff80 100%);
  top: 20%;
  right: 20%;
  box-shadow: 0 0 25px #00ff41;
  animation-delay: 1s;
}

.atom-3 {
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, #ff4500 0%, #ff8040 100%);
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 28px #ff4500;
  animation-delay: 1.5s;
}

.atom-4 {
  width: 13px;
  height: 13px;
  background: radial-gradient(circle, #8a2be2 0%, #b040ff 100%);
  bottom: 20%;
  right: 20%;
  box-shadow: 0 0 22px #8a2be2;
  animation-delay: 2s;
}

.bond {
  position: absolute;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  height: 2px;
  animation: bondGlow 2s ease-in-out infinite;
  box-shadow: 0 0 10px #ffffff;
}

.molecule-1 .bond-1 {
  width: 60px;
  top: 50%;
  left: 30%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.molecule-1 .bond-2 {
  width: 60px;
  top: 50%;
  right: 30%;
  transform: rotate(-45deg);
  animation-delay: 0.7s;
}

.molecule-1 .bond-3 {
  width: 40px;
  bottom: 30%;
  left: 50%;
  transform: translateX(-50%) rotate(90deg);
  animation-delay: 1.4s;
}

/* Chemical Reactions */
.chemical-reactions {
  position: absolute;
  width: 100%;
  height: 100%;
}

.reaction {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 20px;
  animation: reactionFlow 8s ease-in-out infinite;
}

.reaction-1 {
  top: 35%;
  left: 3%;
  animation-delay: 0s;
}

.reaction-2 {
  bottom: 25%;
  right: 5%;
  animation-delay: 4s;
}

.reaction-3 {
  top: 60%;
  left: 70%;
  animation-delay: 8s;
}

.reactant, .product {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  animation: chemicalPulse 2s ease-in-out infinite;
}

.reaction-1 .reactant {
  background: radial-gradient(circle, #ff0080 0%, #ff4080 100%);
  box-shadow: 0 0 25px #ff0080;
}

.reaction-1 .product {
  background: radial-gradient(circle, #00ff41 0%, #40ff80 100%);
  box-shadow: 0 0 25px #00ff41;
  animation-delay: 1s;
}

.reaction-2 .reactant {
  background: radial-gradient(circle, #00ffff 0%, #40ffff 100%);
  box-shadow: 0 0 25px #00ffff;
}

.reaction-2 .product {
  background: radial-gradient(circle, #ff4500 0%, #ff8040 100%);
  box-shadow: 0 0 25px #ff4500;
  animation-delay: 1s;
}

.reaction-3 .reactant {
  background: radial-gradient(circle, #8a2be2 0%, #b040ff 100%);
  box-shadow: 0 0 25px #8a2be2;
}

.reaction-3 .product {
  background: radial-gradient(circle, #00ffff 0%, #40ffff 100%);
  box-shadow: 0 0 25px #00ffff;
  animation-delay: 1s;
}

.arrow {
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 0 20px #ffffff;
  animation: arrowGlow 1.5s ease-in-out infinite;
}

/* Atomic Particles */
.atomic-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.electron {
  position: absolute;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #00ffff 0%, #0080ff 100%);
  border-radius: 50%;
  box-shadow: 0 0 20px #00ffff;
  animation: electronOrbit 6s linear infinite;
}

.electron-1 {
  top: 20%;
  left: 25%;
  animation-delay: 0s;
}

.electron-2 {
  top: 40%;
  right: 20%;
  animation-delay: 1s;
}

.electron-3 {
  bottom: 30%;
  left: 55%;
  animation-delay: 2s;
}

.electron-4 {
  top: 65%;
  left: 10%;
  animation-delay: 3s;
}

.electron-5 {
  top: 12%;
  right: 35%;
  animation-delay: 4s;
}

.electron-6 {
  bottom: 10%;
  right: 25%;
  animation-delay: 5s;
}

.proton {
  position: absolute;
  width: 12px;
  height: 12px;
  background: radial-gradient(circle, #ff0080 0%, #ff4080 100%);
  border-radius: 50%;
  box-shadow: 0 0 25px #ff0080;
  animation: protonFloat 8s ease-in-out infinite;
}

.proton-1 {
  top: 35%;
  left: 45%;
  animation-delay: 0s;
}

.proton-2 {
  bottom: 40%;
  right: 35%;
  animation-delay: 4s;
}

.neutron {
  position: absolute;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, #8a2be2 0%, #b040ff 100%);
  border-radius: 50%;
  box-shadow: 0 0 22px #8a2be2;
  animation: neutronDrift 10s ease-in-out infinite;
}

.neutron-1 {
  top: 55%;
  left: 25%;
  animation-delay: 0s;
}

.neutron-2 {
  bottom: 25%;
  right: 45%;
  animation-delay: 5s;
}

/* Chemical Formulas */
.chemical-formulas {
  position: absolute;
  width: 100%;
  height: 100%;
}

.formula {
  position: absolute;
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 20px currentColor;
  animation: formulaFloat 12s ease-in-out infinite;
  opacity: 0.8;
}

.formula-1 {
  top: 18%;
  left: 55%;
  color: #00ffff;
  animation-delay: 0s;
}

.formula-2 {
  top: 45%;
  left: 15%;
  color: #ff0080;
  animation-delay: 2s;
}

.formula-3 {
  bottom: 25%;
  left: 35%;
  color: #00ff41;
  animation-delay: 4s;
}

.formula-4 {
  top: 70%;
  right: 15%;
  color: #ff4500;
  animation-delay: 6s;
}

.formula-5 {
  top: 25%;
  right: 45%;
  color: #8a2be2;
  animation-delay: 8s;
}

.formula-6 {
  bottom: 40%;
  left: 80%;
  color: #00ffff;
  animation-delay: 10s;
}

.formula-7 {
  top: 55%;
  left: 75%;
  color: #ff0080;
  animation-delay: 12s;
}

.formula-8 {
  bottom: 50%;
  right: 60%;
  color: #00ff41;
  animation-delay: 14s;
}

/* Laboratory Equipment */
.lab-equipment {
  position: absolute;
  width: 100%;
  height: 100%;
}

.beaker {
  position: absolute;
  width: 60px;
  height: 80px;
  border: 3px solid #00ffff;
  border-top: none;
  border-radius: 0 0 20px 20px;
  animation: equipmentGlow 4s ease-in-out infinite;
}

.beaker-1 {
  bottom: 8%;
  left: 15%;
  box-shadow: 0 0 30px #00ffff;
}

.beaker-2 {
  top: 30%;
  left: 5%;
  box-shadow: 0 0 30px #ff0080;
  animation-delay: 2s;
}

.beaker .liquid {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(0deg, #ff0080 0%, #ff4080 100%);
  border-radius: 0 0 17px 17px;
  animation: liquidBubble 3s ease-in-out infinite;
}

.beaker .bubbles {
  position: absolute;
  bottom: 60%;
  left: 50%;
  transform: translateX(-50%);
}

.beaker .bubble {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: bubbleRise 2s ease-in-out infinite;
}

.beaker .bubble:nth-child(1) {
  left: -10px;
  animation-delay: 0s;
}

.beaker .bubble:nth-child(2) {
  left: 0px;
  animation-delay: 0.7s;
}

.beaker .bubble:nth-child(3) {
  left: 10px;
  animation-delay: 1.4s;
}

.test-tube {
  position: absolute;
  width: 20px;
  height: 100px;
  border: 2px solid #00ff41;
  border-radius: 0 0 10px 10px;
  animation: equipmentGlow 5s ease-in-out infinite;
}

.test-tube-1 {
  bottom: 12%;
  right: 20%;
  box-shadow: 0 0 25px #00ff41;
  animation-delay: 2s;
}

.test-tube-2 {
  top: 60%;
  left: 85%;
  box-shadow: 0 0 25px #8a2be2;
  animation-delay: 4s;
}

.test-tube .liquid {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(0deg, #8a2be2 0%, #b040ff 100%);
  border-radius: 0 0 8px 8px;
  animation: liquidGlow 2s ease-in-out infinite;
}

.flask {
  position: absolute;
  width: 50px;
  height: 70px;
  border: 3px solid #ff4500;
  border-radius: 25px 25px 10px 10px;
  animation: equipmentGlow 6s ease-in-out infinite;
}

.flask-1 {
  top: 15%;
  right: 8%;
  box-shadow: 0 0 35px #ff4500;
  animation-delay: 3s;
}

.flask-2 {
  bottom: 35%;
  left: 3%;
  box-shadow: 0 0 35px #00ffff;
  animation-delay: 6s;
}

.flask .liquid {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(0deg, #00ffff 0%, #40ffff 100%);
  border-radius: 22px 22px 7px 7px;
  animation: liquidBubble 2.5s ease-in-out infinite;
}

.flask .vapor {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: vaporRise 3s ease-in-out infinite;
}

/* DNA Helix */
.dna-helix {
  position: absolute;
  top: 8%;
  right: 3%;
  width: 120px;
  height: 350px;
  animation: dnaRotate 15s linear infinite;
}

.helix-strand {
  position: absolute;
  width: 4px;
  height: 100%;
  border-radius: 2px;
  animation: helixGlow 3s ease-in-out infinite;
}

.strand-1 {
  left: 20%;
  background: linear-gradient(0deg, #00ffff 0%, #ff0080 50%, #00ffff 100%);
  box-shadow: 0 0 20px #00ffff;
  transform: rotateY(0deg);
  animation-delay: 0s;
}

.strand-2 {
  right: 20%;
  background: linear-gradient(0deg, #ff0080 0%, #00ffff 50%, #ff0080 100%);
  box-shadow: 0 0 20px #ff0080;
  transform: rotateY(180deg);
  animation-delay: 1.5s;
}

.base-pair {
  position: absolute;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #00ff41 0%, #ffffff 50%, #ff4500 100%);
  left: 50%;
  transform: translateX(-50%);
  animation: basePairPulse 2s ease-in-out infinite;
  box-shadow: 0 0 15px #ffffff;
}

.bp-1 {
  top: 20%;
  animation-delay: 0s;
}

.bp-2 {
  top: 40%;
  animation-delay: 0.5s;
}

.bp-3 {
  top: 60%;
  animation-delay: 1s;
}

.bp-4 {
  top: 80%;
  animation-delay: 1.5s;
}

/* Chemistry Animation Keyframes */
@keyframes moleculeRotate {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.8;
  }
}

@keyframes atomPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

@keyframes bondGlow {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 10px #ffffff;
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 25px #ffffff, 0 0 50px #ffffff;
  }
}

@keyframes reactionFlow {
  0%, 100% {
    transform: translateX(0px);
    opacity: 0.8;
  }
  50% {
    transform: translateX(30px);
    opacity: 1;
  }
}

@keyframes chemicalPulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.2);
    filter: brightness(1.5);
  }
}

@keyframes arrowGlow {
  0%, 100% {
    text-shadow: 0 0 20px #ffffff;
    transform: scale(1);
  }
  50% {
    text-shadow: 0 0 40px #ffffff, 0 0 60px #ffffff;
    transform: scale(1.1);
  }
}

@keyframes electronOrbit {
  0% {
    transform: rotate(0deg) translateX(50px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(50px) rotate(-360deg);
  }
}

@keyframes protonFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  50% {
    transform: translateY(-40px) translateX(-5px);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-20px) translateX(-10px);
    opacity: 1;
  }
}

@keyframes neutronDrift {
  0%, 100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateX(30px) translateY(-15px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateX(-20px) translateY(-30px) rotate(240deg);
    opacity: 0.8;
  }
}

@keyframes formulaFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-30px) rotate(0deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-15px) rotate(-5deg);
    opacity: 1;
  }
}

@keyframes equipmentGlow {
  0%, 100% {
    box-shadow: 0 0 30px currentColor;
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 50px currentColor, 0 0 80px currentColor;
    transform: scale(1.05);
  }
}

@keyframes liquidBubble {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.8;
  }
  50% {
    transform: scaleY(1.1);
    opacity: 1;
  }
}

@keyframes liquidGlow {
  0%, 100% {
    filter: brightness(1);
    opacity: 0.8;
  }
  50% {
    filter: brightness(1.3);
    opacity: 1;
  }
}

@keyframes bubbleRise {
  0% {
    transform: translateY(0px) scale(1);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: translateY(-50px) scale(1.5);
    opacity: 0;
  }
}

@keyframes vaporRise {
  0% {
    transform: translateX(-50%) translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateX(-50%) translateY(-20px) scale(1.3);
    opacity: 0.3;
  }
  100% {
    transform: translateX(-50%) translateY(-40px) scale(1.8);
    opacity: 0;
  }
}

@keyframes dnaRotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

/* Coding Animation Keyframes */
@keyframes codeFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) rotate(1deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-10px) rotate(-1deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) rotate(0.5deg);
    opacity: 0.9;
  }
}

@keyframes tagFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  33% {
    transform: translateY(-15px) scale(1.05);
    opacity: 1;
  }
  66% {
    transform: translateY(-5px) scale(0.95);
    opacity: 0.9;
  }
}

@keyframes typingEffect {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes typeWriter1 {
  0% {
    width: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  20% {
    width: 100%;
  }
  80% {
    width: 100%;
    opacity: 1;
  }
  90% {
    opacity: 0;
  }
  100% {
    width: 0;
    opacity: 0;
  }
}

@keyframes typeWriter2 {
  0% {
    width: 0;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  30% {
    width: 100%;
  }
  70% {
    width: 100%;
    opacity: 1;
  }
  85% {
    opacity: 0;
  }
  100% {
    width: 0;
    opacity: 0;
  }
}

@keyframes helixGlow {
  0%, 100% {
    box-shadow: 0 0 20px currentColor;
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 40px currentColor, 0 0 60px currentColor;
    opacity: 1;
  }
}

@keyframes basePairPulse {
  0%, 100% {
    transform: translateX(-50%) scaleX(1);
    box-shadow: 0 0 15px #ffffff;
  }
  50% {
    transform: translateX(-50%) scaleX(1.2);
    box-shadow: 0 0 30px #ffffff, 0 0 45px #ffffff;
  }
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-25%) translateY(-10px);
  }
  100% {
    transform: translateX(0%) translateY(0px);
  }
}

/* Hover effects for enhanced interactivity */
.feature-card .card-action {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.feature-card:hover .card-action {
  opacity: 1;
  transform: translateY(0);
}

/* Add subtle animations to text elements */
.feature-card h3 {
  transition: all 0.3s ease;
}

.feature-card:hover h3 {
  color: #667eea;
  transform: translateY(-2px);
}

.feature-card p {
  transition: all 0.3s ease;
}

.feature-card:hover p {
  color: #5a6c7d;
}

/* Loading state animations */
.hero-section.loaded .hero-title {
  animation-play-state: running;
}

.hero-section.loaded .hero-subtitle {
  animation-play-state: running;
}

/* Enhanced button animations */
.btn {
  position: relative;
  overflow: hidden;
  transform: perspective(1px) translateZ(0);
  transition: all 0.3s ease;
}

.btn:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 100%);
  transform: scaleX(0);
  transform-origin: 0 50%;
  transition: transform 0.3s ease-out;
}

.btn:hover:before {
  transform: scaleX(1);
}

/* Staggered animation delays for feature cards */
.features-section {
  animation: fadeInUp 1s ease-out 1.2s both;
  opacity: 0;
}

/* Add subtle parallax effect to icons */
.feature-icon {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: translateZ(20px) scale(1.2) rotateY(5deg);
}

/* Responsive animation adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    animation-duration: 0.8s;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    animation-duration: 0.8s;
  }

  .feature-card {
    animation-duration: 0.6s;
  }

  .feature-card:hover {
    transform: translateY(-5px) scale(1.01);
  }

  /* Reduce background animations on mobile for performance */
  .background-particles {
    display: none;
  }

  .floating-shapes .shape {
    opacity: 0.05;
  }

  .stars-container .star {
    font-size: 1rem;
  }

  .waves-container {
    height: 100px;
  }

  .page-container::before {
    animation-duration: 20s;
  }

  .page-container::after {
    animation-duration: 25s;
  }
}

/* High performance mode for older devices */
@media (max-width: 480px) {
  .floating-shapes,
  .stars-container,
  .waves-container {
    display: none;
  }

  .page-container::after {
    display: none;
  }

  /* Simplify chemistry animations on mobile */
  .molecules .molecule {
    animation-duration: 30s;
  }

  .atomic-particles .electron {
    animation-duration: 8s;
  }

  .chemical-formulas .formula {
    font-size: 14px;
  }

  .lab-equipment {
    opacity: 0.5;
  }

  .dna-helix {
    animation-duration: 20s;
  }

  /* Simplify control buttons on mobile */
  .control-buttons {
    top: 10px;
    right: 10px;
    gap: 10px;
  }

  .control-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .control-btn span {
    display: none;
  }

  /* Simplify coding animation on mobile */
  .code-block {
    font-size: 10px;
    padding: 10px;
  }

  .tag-item {
    font-size: 10px;
    padding: 6px 12px;
  }

  .typing-line {
    font-size: 12px;
    padding: 8px 12px;
    min-width: 150px;
  }

  /* Hide some elements on very small screens */
  .code-block-3,
  .code-block-4,
  .tag-7,
  .tag-8,
  .tag-9,
  .tag-10,
  .tag-11,
  .tag-12 {
    display: none;
  }
}

@media (max-width: 768px) {
  /* Reduce chemistry complexity on tablets */
  .atomic-particles .proton,
  .atomic-particles .neutron {
    opacity: 0.7;
  }

  .chemical-reactions .reaction {
    animation-duration: 10s;
  }

  .lab-equipment .beaker,
  .lab-equipment .test-tube,
  .lab-equipment .flask {
    transform: scale(0.8);
  }
}

/* Add glow effect on focus for accessibility */
.btn:focus,
.feature-card:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Animated Background Particles */
.background-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: glowingParticleFloat 15s linear infinite;
  box-shadow: 0 0 20px currentColor;
}

.particle-1 {
  width: 6px;
  height: 6px;
  left: 10%;
  background: #00ffff;
  animation-delay: 0s;
  animation-duration: 10s;
  box-shadow: 0 0 30px #00ffff, 0 0 60px #00ffff;
}

.particle-2 {
  width: 8px;
  height: 8px;
  left: 20%;
  background: #ff0080;
  animation-delay: 2s;
  animation-duration: 12s;
  box-shadow: 0 0 25px #ff0080, 0 0 50px #ff0080;
}

.particle-3 {
  width: 4px;
  height: 4px;
  left: 30%;
  background: #8a2be2;
  animation-delay: 4s;
  animation-duration: 14s;
  box-shadow: 0 0 20px #8a2be2, 0 0 40px #8a2be2;
}

.particle-4 {
  width: 7px;
  height: 7px;
  left: 60%;
  background: #00ff41;
  animation-delay: 1s;
  animation-duration: 11s;
  box-shadow: 0 0 28px #00ff41, 0 0 56px #00ff41;
}

.particle-5 {
  width: 5px;
  height: 5px;
  left: 80%;
  background: #ff4500;
  animation-delay: 3s;
  animation-duration: 13s;
  box-shadow: 0 0 22px #ff4500, 0 0 44px #ff4500;
}

.particle-6 {
  width: 9px;
  height: 9px;
  left: 90%;
  background: #ff1493;
  animation-delay: 5s;
  animation-duration: 9s;
  box-shadow: 0 0 35px #ff1493, 0 0 70px #ff1493;
}

/* Floating Geometric Shapes */
.floating-shapes {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.shape {
  position: absolute;
  opacity: 0.1;
}

.shape-circle {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #ff0080, #00ffff, #8a2be2, #ff4500);
  background-size: 400% 400%;
  border-radius: 50%;
  top: 15%;
  left: 75%;
  animation: rotateFloat 18s linear infinite, colorShift 8s ease infinite;
  box-shadow: 0 0 60px rgba(255, 0, 128, 0.6), 0 0 120px rgba(0, 255, 255, 0.4);
  opacity: 0.8;
}

.shape-triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 90px solid #00ff41;
  top: 65%;
  left: 8%;
  animation: triangleFloat 20s linear infinite, triangleGlow 4s ease infinite;
  filter: drop-shadow(0 0 30px #00ff41) drop-shadow(0 0 60px #00ff41);
}

.shape-square {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff1493, #8a2be2, #00ffff);
  background-size: 300% 300%;
  top: 35%;
  left: 3%;
  animation: squareRotate 12s linear infinite, squareColorShift 6s ease infinite;
  transform-origin: center;
  box-shadow: 0 0 40px rgba(255, 20, 147, 0.7), 0 0 80px rgba(138, 43, 226, 0.5);
  border-radius: 15px;
}

.shape-hexagon {
  width: 100px;
  height: 58px;
  background: #ff4500;
  position: relative;
  top: 75%;
  right: 3%;
  animation: hexagonFloat 15s ease-in-out infinite, hexagonGlow 5s ease infinite;
  box-shadow: 0 0 50px #ff4500, 0 0 100px rgba(255, 69, 0, 0.6);
}

.shape-hexagon:before,
.shape-hexagon:after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
}

.shape-hexagon:before {
  bottom: 100%;
  border-bottom: 29px solid #ff4500;
  filter: drop-shadow(0 0 20px #ff4500);
}

.shape-hexagon:after {
  top: 100%;
  border-top: 29px solid #ff4500;
  filter: drop-shadow(0 0 20px #ff4500);
}

/* Animated Stars */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.star {
  position: absolute;
  font-size: 2rem;
  animation: intenseTwinkle 2s ease-in-out infinite;
  text-shadow: 0 0 20px currentColor, 0 0 40px currentColor;
}

.star-1 {
  top: 12%;
  left: 12%;
  color: #00ffff;
  animation-delay: 0s;
  text-shadow: 0 0 30px #00ffff, 0 0 60px #00ffff;
}

.star-2 {
  top: 22%;
  right: 18%;
  color: #ff0080;
  animation-delay: 0.7s;
  text-shadow: 0 0 25px #ff0080, 0 0 50px #ff0080;
}

.star-3 {
  top: 55%;
  left: 22%;
  color: #8a2be2;
  animation-delay: 1.4s;
  text-shadow: 0 0 28px #8a2be2, 0 0 56px #8a2be2;
}

.star-4 {
  top: 72%;
  right: 28%;
  color: #00ff41;
  animation-delay: 0.3s;
  text-shadow: 0 0 32px #00ff41, 0 0 64px #00ff41;
}

.star-5 {
  top: 38%;
  right: 8%;
  color: #ff4500;
  animation-delay: 1.1s;
  text-shadow: 0 0 26px #ff4500, 0 0 52px #ff4500;
}

.star-6 {
  top: 85%;
  left: 45%;
  color: #ff1493;
  animation-delay: 0.9s;
  text-shadow: 0 0 35px #ff1493, 0 0 70px #ff1493;
}

.star-7 {
  top: 8%;
  left: 60%;
  color: #00ff41;
  animation-delay: 1.8s;
  text-shadow: 0 0 40px #00ff41, 0 0 80px #00ff41;
}

.star-8 {
  top: 50%;
  left: 85%;
  color: #8a2be2;
  animation-delay: 0.6s;
  text-shadow: 0 0 30px #8a2be2, 0 0 60px #8a2be2;
}

/* Neon Grid Lines */
.neon-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  animation: neonPulse 4s ease-in-out infinite;
}

.grid-line.horizontal {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
}

.grid-line.vertical {
  width: 2px;
  height: 100%;
  background: linear-gradient(0deg, transparent, #ff0080, transparent);
}

.line-1 {
  top: 30%;
  animation-delay: 0s;
  box-shadow: 0 0 20px #00ffff;
}

.line-2 {
  top: 70%;
  animation-delay: 2s;
  box-shadow: 0 0 20px #00ffff;
}

.line-3 {
  left: 25%;
  animation-delay: 1s;
  box-shadow: 0 0 20px #ff0080;
}

.line-4 {
  left: 75%;
  animation-delay: 3s;
  box-shadow: 0 0 20px #ff0080;
}

/* Floating Orbs */
.floating-orbs {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.orb {
  position: absolute;
  border-radius: 50%;
  animation: orbFloat 8s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  box-shadow: 0 0 100px rgba(0, 255, 255, 0.5);
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(255, 0, 128, 0.3) 0%, transparent 70%);
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  box-shadow: 0 0 80px rgba(255, 0, 128, 0.5);
}

.orb-3 {
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3) 0%, transparent 70%);
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
  box-shadow: 0 0 90px rgba(138, 43, 226, 0.5);
}

/* Animated Waves */
.waves-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50% 50% 0 0;
}

.wave-1 {
  animation: waveMove 8s ease-in-out infinite;
  opacity: 0.3;
}

.wave-2 {
  animation: waveMove 12s ease-in-out infinite reverse;
  opacity: 0.2;
  animation-delay: 2s;
}

.wave-3 {
  animation: waveMove 15s ease-in-out infinite;
  opacity: 0.1;
  animation-delay: 4s;
}

/* Flying Planes Background */
.planes-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: -4;
  overflow: hidden;
  opacity: 0.8;
}

.planes-background.dark-mode {
  background: linear-gradient(135deg,
    rgba(25, 25, 112, 0.3) 0%,
    rgba(72, 61, 139, 0.2) 25%,
    rgba(30, 144, 255, 0.1) 50%,
    rgba(135, 206, 235, 0.2) 75%,
    rgba(25, 25, 112, 0.3) 100%);
}

.planes-background.light-mode {
  background: linear-gradient(135deg,
    rgba(135, 206, 235, 0.3) 0%,
    rgba(173, 216, 230, 0.2) 25%,
    rgba(240, 248, 255, 0.1) 50%,
    rgba(176, 224, 230, 0.2) 75%,
    rgba(135, 206, 235, 0.3) 100%);
}

/* Flying Planes */
.plane {
  position: absolute;
  display: flex;
  align-items: center;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.plane-body {
  font-size: 24px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  z-index: 2;
}

.plane-trail {
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%);
  margin-left: -5px;
  border-radius: 1px;
  animation: trailFade 3s ease-in-out infinite;
}

.dark-mode .plane-trail {
  background: linear-gradient(90deg,
    rgba(135, 206, 235, 0.8) 0%,
    rgba(135, 206, 235, 0.4) 50%,
    rgba(135, 206, 235, 0) 100%);
}

/* Individual Plane Animations */
.plane-1 {
  top: 15%;
  left: -100px;
  animation: flyAcross1 25s linear infinite;
}

.plane-2 {
  top: 35%;
  right: -100px;
  animation: flyAcross2 30s linear infinite;
  animation-delay: -5s;
}

.plane-3 {
  top: 55%;
  left: -100px;
  animation: flyAcross3 35s linear infinite;
  animation-delay: -10s;
}

.plane-4 {
  top: 75%;
  right: -100px;
  animation: flyAcross4 28s linear infinite;
  animation-delay: -15s;
}

.plane-5 {
  top: 25%;
  left: -100px;
  animation: flyAcross5 32s linear infinite;
  animation-delay: -20s;
}

/* Flight Path Keyframes */
@keyframes flyAcross1 {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
}

@keyframes flyAcross2 {
  0% { transform: translateX(100px); }
  100% { transform: translateX(calc(-100vw - 100px)); }
}

@keyframes flyAcross3 {
  0% { transform: translateX(-100px) translateY(0px); }
  25% { transform: translateX(25vw) translateY(-20px); }
  75% { transform: translateX(75vw) translateY(20px); }
  100% { transform: translateX(calc(100vw + 100px)) translateY(0px); }
}

@keyframes flyAcross4 {
  0% { transform: translateX(100px) translateY(0px); }
  30% { transform: translateX(70vw) translateY(15px); }
  70% { transform: translateX(30vw) translateY(-15px); }
  100% { transform: translateX(calc(-100vw - 100px)) translateY(0px); }
}

@keyframes flyAcross5 {
  0% { transform: translateX(-100px) translateY(0px); }
  20% { transform: translateX(20vw) translateY(-30px); }
  50% { transform: translateX(50vw) translateY(10px); }
  80% { transform: translateX(80vw) translateY(-10px); }
  100% { transform: translateX(calc(100vw + 100px)) translateY(0px); }
}

@keyframes trailFade {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* Clouds */
.cloud {
  position: absolute;
  font-size: 24px;
  filter: drop-shadow(1px 1px 3px rgba(0, 0, 0, 0.2));
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

.cloud-1 {
  top: 10%;
  left: 10%;
  animation: cloudFloat1 20s ease-in-out infinite;
}

.cloud-2 {
  top: 20%;
  right: 15%;
  animation: cloudFloat2 25s ease-in-out infinite;
  animation-delay: -5s;
}

.cloud-3 {
  top: 40%;
  left: 20%;
  animation: cloudFloat3 30s ease-in-out infinite;
  animation-delay: -10s;
}

.cloud-4 {
  top: 60%;
  right: 25%;
  animation: cloudFloat4 22s ease-in-out infinite;
  animation-delay: -15s;
}

.cloud-5 {
  top: 80%;
  left: 30%;
  animation: cloudFloat5 28s ease-in-out infinite;
  animation-delay: -20s;
}

.cloud-6 {
  top: 30%;
  right: 40%;
  animation: cloudFloat6 35s ease-in-out infinite;
  animation-delay: -25s;
}

/* Cloud Float Keyframes */
@keyframes cloudFloat1 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(20px) translateY(-10px); }
  50% { transform: translateX(40px) translateY(5px); }
  75% { transform: translateX(20px) translateY(-5px); }
}

@keyframes cloudFloat2 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  33% { transform: translateX(-15px) translateY(8px); }
  66% { transform: translateX(-30px) translateY(-12px); }
}

@keyframes cloudFloat3 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  20% { transform: translateX(25px) translateY(15px); }
  40% { transform: translateX(50px) translateY(-8px); }
  60% { transform: translateX(30px) translateY(12px); }
  80% { transform: translateX(10px) translateY(-5px); }
}

@keyframes cloudFloat4 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  50% { transform: translateX(-40px) translateY(20px); }
}

@keyframes cloudFloat5 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(30px) translateY(-15px); }
  75% { transform: translateX(-20px) translateY(10px); }
}

@keyframes cloudFloat6 {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  30% { transform: translateX(-25px) translateY(-20px); }
  70% { transform: translateX(35px) translateY(15px); }
}

/* Sky Elements */
.sky-element {
  position: absolute;
  font-size: 20px;
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

.sun {
  top: 5%;
  right: 10%;
  animation: sunGlow 4s ease-in-out infinite;
}

.birds {
  animation: birdFly 15s linear infinite;
}

.birds:nth-child(1) {
  top: 12%;
  left: -50px;
  animation-delay: 0s;
}

.birds:nth-child(2) {
  top: 18%;
  left: -50px;
  animation-delay: -3s;
}

.birds:nth-child(3) {
  top: 22%;
  left: -50px;
  animation-delay: -6s;
}

.birds:nth-child(4) {
  top: 28%;
  left: -50px;
  animation-delay: -9s;
}

/* Sky Element Keyframes */
@keyframes sunGlow {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
  }
}

@keyframes birdFly {
  0% { transform: translateX(-50px); }
  100% { transform: translateX(calc(100vw + 50px)); }
}

/* Responsive Design for Planes */
@media (max-width: 768px) {
  .planes-background {
    opacity: 0.6;
  }

  .plane-body {
    font-size: 20px;
  }

  .plane-trail {
    width: 60px;
    height: 1px;
  }

  .cloud {
    font-size: 20px;
  }

  .sky-element {
    font-size: 18px;
  }

  /* Reduce animation complexity on mobile */
  .plane-3, .plane-5 {
    animation-name: flyAcross1;
  }

  .plane-4 {
    animation-name: flyAcross2;
  }
}

@media (max-width: 480px) {
  .planes-background {
    opacity: 0.4;
  }

  .plane-body {
    font-size: 16px;
  }

  .cloud {
    font-size: 16px;
  }

  .sky-element {
    font-size: 14px;
  }

  .plane-trail {
    width: 40px;
  }
}
