/* Common Page Styles */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 1.2rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto 2rem auto;
  line-height: 1.6;
}

/* Create Note Button */
.create-note-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.create-note-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-container h2 {
  color: #7f8c8d;
  font-weight: 400;
}

/* Home Page Styles */
.page-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

/* Animated Background Layers */
.page-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientWave 15s ease infinite;
  z-index: -3;
}

.page-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: floatingBubbles 20s ease-in-out infinite;
  z-index: -2;
}

/* Floating Geometric Shapes */
.page-content {
  position: relative;
  z-index: 1;
}

.page-content::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float1 6s ease-in-out infinite;
  z-index: -1;
}

.page-content::after {
  content: '';
  position: absolute;
  top: 60%;
  right: 15%;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.08);
  transform: rotate(45deg);
  animation: float2 8s ease-in-out infinite;
  z-index: -1;
}

.hero-section {
  text-align: center;
  padding: 4rem 0;
  margin-bottom: 4rem;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1s ease-out;
}

/* Animated background gradient */
.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 25%,
    rgba(102, 126, 234, 0.1) 50%,
    rgba(118, 75, 162, 0.1) 75%,
    rgba(102, 126, 234, 0.1) 100%);
  animation: gradientShift 8s ease-in-out infinite;
  z-index: -1;
}

/* Interactive mouse-following effect */
.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle 300px at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(102, 126, 234, 0.05) 0%,
    transparent 50%
  );
  pointer-events: none;
  z-index: -1;
  transition: all 0.3s ease;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slideInFromLeft 1s ease-out 0.3s both;
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: expandLine 1.5s ease-out 1s both;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #7f8c8d;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  animation: slideInFromRight 1s ease-out 0.6s both;
  opacity: 0;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.9s both;
  opacity: 0;
}

.hero-buttons .btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hero-buttons .btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.hero-buttons .btn:hover::before {
  width: 300px;
  height: 300px;
}

.hero-buttons .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hero-buttons .btn:active {
  transform: translateY(-1px);
  animation: pulse 0.3s ease;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.features-section {
  margin-top: 4rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease-out forwards;
}

.feature-card:nth-child(1) {
  animation-delay: 0.2s;
}

.feature-card:nth-child(2) {
  animation-delay: 0.4s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.6s;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: all 0.6s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.feature-card:active {
  transform: translateY(-5px) scale(0.98);
}

/* Clickable Card Styles */
.clickable-card {
  cursor: pointer;
}

.clickable-card:hover .card-action {
  opacity: 1;
  transform: translateY(0);
}

.card-action {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  opacity: 0;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clickable-card:active {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  animation: bounce 0.6s ease;
  transform: scale(1.2);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Java Notes Page Styles */
.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.topic-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.topic-card:hover {
  transform: translateY(-5px);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.topic-title {
  color: #2c3e50;
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
}

.topic-level {
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 1rem;
}

.topic-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.topic-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.coming-soon {
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.coming-soon h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.coming-soon p {
  color: #7f8c8d;
}

/* Notes Page Styles */
.notes-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.category-filter {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.note-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.note-card:hover {
  transform: translateY(-5px);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.note-title {
  color: #2c3e50;
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
}

.note-category {
  background: #ecf0f1;
  color: #7f8c8d;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  margin-left: 1rem;
}

.note-preview {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note-date {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
}

.note-button {
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.note-button.view-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.note-button.view-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.note-button.edit-btn {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid #3498db;
}

.note-button.edit-btn:hover {
  background: #3498db;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.no-results {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.no-results h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.no-results p {
  color: #7f8c8d;
}

/* Contact Page Styles */
.contact-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-top: 2rem;
}

.contact-info {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.contact-info h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.contact-label {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
  text-align: center;
  margin-top: 0.5rem;
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  text-align: center;
}

.contact-item h4 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.contact-item p {
  color: #7f8c8d;
  margin: 0;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-content {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .notes-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .contact-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .topic-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .topic-level {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .note-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .note-category {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .note-actions {
    flex-direction: column;
    gap: 0.3rem;
  }

  .note-button {
    width: 100%;
    text-align: center;
  }
}

/* Java Syllabus Styles */
.java-syllabus-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.syllabus-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.syllabus-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.syllabus-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.syllabus-icon {
  font-size: 3rem;
  margin-right: 1rem;
}

.syllabus-info {
  flex: 1;
}

.syllabus-title {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
}

.syllabus-difficulty {
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.syllabus-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.syllabus-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: #95a5a6;
}

.syllabus-duration,
.syllabus-topics {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.syllabus-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.syllabus-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Learning Path Styles */
.learning-path {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 3rem;
  text-align: center;
}

.learning-path h3 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.path-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.path-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
}

.step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.step-text {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 600;
}

.path-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* Topic Modal Styles */
.topic-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
  backdrop-filter: blur(5px);
}

.topic-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.topic-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-title-section {
  display: flex;
  align-items: center;
}

.modal-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
}

.modal-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.topic-modal-content {
  padding: 2rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.modal-section {
  margin-bottom: 2rem;
}

.modal-section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.course-meta {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
}

.difficulty-badge {
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.topics-list {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.5rem;
}

.topic-item {
  background: #f8f9fa;
  padding: 0.8rem 1rem;
  border-radius: 10px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.topic-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.study-materials {
  display: grid;
  gap: 1rem;
}

.material-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.material-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.material-header {
  margin-bottom: 1rem;
}

.material-type {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: inline-block;
}

.material-title {
  margin: 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.material-author,
.material-platform {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.material-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.material-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.material-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

.code-example {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1.5rem;
  border-radius: 10px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

.code-example code {
  background: none;
  color: inherit;
  padding: 0;
}

/* Responsive Styles for Java Syllabus */
@media (max-width: 768px) {
  .java-syllabus-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .syllabus-card {
    padding: 1.5rem;
  }

  .syllabus-header {
    flex-direction: column;
    text-align: center;
  }

  .syllabus-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .syllabus-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .path-steps {
    flex-direction: column;
    gap: 1.5rem;
  }

  .path-arrow {
    transform: rotate(90deg);
  }

  .topic-modal {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .topic-modal-header {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }

  .modal-title-section {
    margin-bottom: 1rem;
  }

  .topic-modal-content {
    padding: 1.5rem;
  }

  .course-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .topics-list {
    grid-template-columns: 1fr;
  }

  .material-card {
    padding: 1rem;
  }

  .code-example {
    font-size: 0.8rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .syllabus-card {
    padding: 1rem;
  }

  .learning-path {
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.4rem;
  }

  .modal-icon {
    font-size: 2rem;
  }

  .topic-modal-content {
    padding: 1rem;
  }
}

/* Custom scrollbar for topic modal */
.topic-modal-content::-webkit-scrollbar {
  width: 6px;
}

.topic-modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.topic-modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.topic-modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* Enhanced contact item styling */
.contact-item {
  margin-bottom: 1rem;
  padding: 0;
  background: transparent;
  border-radius: 15px;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-icon-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  text-decoration: none;
  border: 2px solid transparent;
}

.contact-icon-link:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: rgba(102, 126, 234, 0.5);
}

.contact-icon-link .contact-icon {
  font-size: 2rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  line-height: 1;
  overflow: hidden;
  text-align: center;
}

.contact-icon-link:hover .contact-icon {
  transform: scale(1.2);
  filter: brightness(1.2);
}

.contact-intro {
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-style: italic;
  text-align: center;
}

/* Contact info grid layout */
.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contact-info h3 {
  grid-column: 1 / -1;
  text-align: center;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.contact-intro {
  grid-column: 1 / -1;
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Responsive adjustments for contact page */
@media (max-width: 768px) {
  .contact-info {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 1rem;
    max-width: 400px;
  }

  .contact-icon-link {
    width: 70px;
    height: 70px;
    border-radius: 15px;
  }

  .contact-icon-link .contact-icon {
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
  }

  .contact-item {
    margin-bottom: 0.5rem;
  }

  .contact-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .contact-info {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem;
  }

  .contact-icon-link {
    width: 60px;
    height: 60px;
  }

  .contact-icon-link .contact-icon {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
  }

  .contact-label {
    font-size: 0.7rem;
  }
}

/* Full-Page Modal Styles */
.fullpage-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.fullpage-modal {
  width: 95vw;
  height: 95vh;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.fullpage-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.fullpage-modal-header .modal-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.fullpage-modal-header .modal-icon {
  font-size: 2.5rem;
}

.fullpage-modal-header .modal-title {
  font-size: 2rem;
  margin: 0;
  font-weight: 700;
}

.fullpage-modal-header .modal-difficulty {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.fullpage-modal-header .modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fullpage-modal-header .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Level Selection Tabs */
.level-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.level-tab {
  flex: 1;
  padding: 1rem 2rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6c757d;
}

.level-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.level-tab.active {
  background: white;
  color: #667eea;
  border-bottom: 3px solid #667eea;
}

/* Modal Content */
.fullpage-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: #f8f9fa;
}

.level-content {
  max-width: 1200px;
  margin: 0 auto;
}

.level-header {
  text-align: center;
  margin-bottom: 3rem;
}

.level-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.level-description {
  font-size: 1.2rem;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Concepts Grid */
.concepts-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.concept-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.concept-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.concept-header h3 {
  color: #667eea;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
  padding-bottom: 0.5rem;
}

.concept-explanation {
  margin-bottom: 1.5rem;
}

.concept-explanation p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #495057;
}

.concept-example h4 {
  color: #28a745;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 10px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  border-left: 4px solid #667eea;
}

.code-block code {
  color: inherit;
  background: none;
  padding: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fullpage-modal {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .fullpage-modal-header {
    padding: 1rem;
  }

  .fullpage-modal-header .modal-title {
    font-size: 1.5rem;
  }

  .level-tabs {
    flex-direction: column;
  }

  .level-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .fullpage-modal-content {
    padding: 1rem;
  }

  .level-header h2 {
    font-size: 2rem;
  }

  .concept-card {
    padding: 1.5rem;
  }

  .code-block {
    font-size: 0.8rem;
    padding: 1rem;
  }
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from {
    width: 0;
  }
  to {
    width: 100px;
  }
}

@keyframes gradientShift {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  75% {
    transform: rotate(270deg) scale(1.1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.1);
  }
  60% {
    transform: translateY(-5px) scale(1.05);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Background Animation Keyframes */
@keyframes gradientWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes floatingBubbles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: translateY(-60px) rotate(240deg);
    opacity: 0.6;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes float1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(10px);
  }
  66% {
    transform: translateY(-10px) translateX(-5px);
  }
}

@keyframes float2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(45deg);
  }
  50% {
    transform: translateY(-30px) translateX(15px) rotate(225deg);
  }
}

@keyframes rotateFloat {
  0% {
    transform: rotate(0deg) translateY(0px);
  }
  50% {
    transform: rotate(180deg) translateY(-20px);
  }
  100% {
    transform: rotate(360deg) translateY(0px);
  }
}

@keyframes triangleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
}

@keyframes squareRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    transform: rotate(180deg) scale(1);
  }
  75% {
    transform: rotate(270deg) scale(0.9);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes hexagonFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-25px) rotate(120deg);
  }
  66% {
    transform: translateY(-50px) rotate(240deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-25%) translateY(-10px);
  }
  100% {
    transform: translateX(0%) translateY(0px);
  }
}

/* Hover effects for enhanced interactivity */
.feature-card .card-action {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.feature-card:hover .card-action {
  opacity: 1;
  transform: translateY(0);
}

/* Add subtle animations to text elements */
.feature-card h3 {
  transition: all 0.3s ease;
}

.feature-card:hover h3 {
  color: #667eea;
  transform: translateY(-2px);
}

.feature-card p {
  transition: all 0.3s ease;
}

.feature-card:hover p {
  color: #5a6c7d;
}

/* Loading state animations */
.hero-section.loaded .hero-title {
  animation-play-state: running;
}

.hero-section.loaded .hero-subtitle {
  animation-play-state: running;
}

/* Enhanced button animations */
.btn {
  position: relative;
  overflow: hidden;
  transform: perspective(1px) translateZ(0);
  transition: all 0.3s ease;
}

.btn:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 100%);
  transform: scaleX(0);
  transform-origin: 0 50%;
  transition: transform 0.3s ease-out;
}

.btn:hover:before {
  transform: scaleX(1);
}

/* Staggered animation delays for feature cards */
.features-section {
  animation: fadeInUp 1s ease-out 1.2s both;
  opacity: 0;
}

/* Add subtle parallax effect to icons */
.feature-icon {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: translateZ(20px) scale(1.2) rotateY(5deg);
}

/* Responsive animation adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    animation-duration: 0.8s;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    animation-duration: 0.8s;
  }

  .feature-card {
    animation-duration: 0.6s;
  }

  .feature-card:hover {
    transform: translateY(-5px) scale(1.01);
  }

  /* Reduce background animations on mobile for performance */
  .background-particles {
    display: none;
  }

  .floating-shapes .shape {
    opacity: 0.05;
  }

  .stars-container .star {
    font-size: 1rem;
  }

  .waves-container {
    height: 100px;
  }

  .page-container::before {
    animation-duration: 20s;
  }

  .page-container::after {
    animation-duration: 25s;
  }
}

/* High performance mode for older devices */
@media (max-width: 480px) {
  .floating-shapes,
  .stars-container,
  .waves-container {
    display: none;
  }

  .page-container::after {
    display: none;
  }
}

/* Add glow effect on focus for accessibility */
.btn:focus,
.feature-card:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Animated Background Particles */
.background-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
}

.particle-1 {
  width: 4px;
  height: 4px;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 12s;
}

.particle-2 {
  width: 6px;
  height: 6px;
  left: 20%;
  animation-delay: 2s;
  animation-duration: 15s;
}

.particle-3 {
  width: 3px;
  height: 3px;
  left: 30%;
  animation-delay: 4s;
  animation-duration: 18s;
}

.particle-4 {
  width: 5px;
  height: 5px;
  left: 60%;
  animation-delay: 1s;
  animation-duration: 14s;
}

.particle-5 {
  width: 4px;
  height: 4px;
  left: 80%;
  animation-delay: 3s;
  animation-duration: 16s;
}

.particle-6 {
  width: 7px;
  height: 7px;
  left: 90%;
  animation-delay: 5s;
  animation-duration: 13s;
}

/* Floating Geometric Shapes */
.floating-shapes {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.shape {
  position: absolute;
  opacity: 0.1;
}

.shape-circle {
  width: 120px;
  height: 120px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  top: 20%;
  left: 80%;
  animation: rotateFloat 20s linear infinite;
}

.shape-triangle {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 70px solid rgba(255, 107, 107, 0.3);
  top: 70%;
  left: 10%;
  animation: triangleFloat 25s linear infinite;
}

.shape-square {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: 40%;
  left: 5%;
  animation: squareRotate 15s linear infinite;
  transform-origin: center;
}

.shape-hexagon {
  width: 80px;
  height: 46px;
  background: rgba(118, 75, 162, 0.3);
  position: relative;
  top: 80%;
  right: 5%;
  animation: hexagonFloat 18s ease-in-out infinite;
}

.shape-hexagon:before,
.shape-hexagon:after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
}

.shape-hexagon:before {
  bottom: 100%;
  border-bottom: 23px solid rgba(118, 75, 162, 0.3);
}

.shape-hexagon:after {
  top: 100%;
  border-top: 23px solid rgba(118, 75, 162, 0.3);
}

/* Animated Stars */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.star {
  position: absolute;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.5rem;
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.star-2 {
  top: 25%;
  right: 20%;
  animation-delay: 1s;
}

.star-3 {
  top: 60%;
  left: 25%;
  animation-delay: 2s;
}

.star-4 {
  top: 75%;
  right: 30%;
  animation-delay: 0.5s;
}

.star-5 {
  top: 40%;
  right: 10%;
  animation-delay: 1.5s;
}

/* Animated Waves */
.waves-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50% 50% 0 0;
}

.wave-1 {
  animation: waveMove 8s ease-in-out infinite;
  opacity: 0.3;
}

.wave-2 {
  animation: waveMove 12s ease-in-out infinite reverse;
  opacity: 0.2;
  animation-delay: 2s;
}

.wave-3 {
  animation: waveMove 15s ease-in-out infinite;
  opacity: 0.1;
  animation-delay: 4s;
}
